import 'dart:async';
import 'dart:convert';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/profile/sub_pages/register/widgets/feedback.dart';
import 'package:amor_app/pages/session/logic/session_action_tip.dart';
import 'package:amor_app/pages/session/logic/session_sqlite.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/gems_short_dialog.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'logic/session_socket.dart';
import 'logic/session_voice_play.dart';
import 'state.dart';
import 'widgets/dialog_widgets/image_message_preview.dart';

class SessionPageController extends GetxController {
  static SessionPageController get to => Get.find();
  final state = SessionPageState();
  late EasyRefreshController refreshController;
  late ScrollController scrollController;
  //逐字输出计时器
  Timer? textOutputTimer;
  SessionActionTipLogic msgTipLogic = SessionActionTipLogic();
  late SessionVoicePlay voicePlay;
  SessionSocket socket = SessionSocket();

  @override
  onReady() async {
    super.onReady;
    //用户设置的震动开关
    state.vibrationEnable = (Get.find<SPService>().get(spHipticsValue) ?? true) as bool;
    Analytics().logEvent(Analytics.view, screen: Analytics.pageChatwin);
    try {
      state.undressShowCount = RemoteConfigUtil.getConfig<int>(key: undressShowCount);
      print('undressShowCount - ${state.undressShowCount}');
    } catch (e) {}
  }

  @override
  void onInit() {
    super.onInit();
    state.socketText.clear();
    if (Get.arguments != null) {
      state.sessionNo = Get.arguments;
    } else if (Get.parameters.containsKey('sessionNo')) {
      state.sessionNo = Get.parameters['sessionNo'] ?? '';
    }
    state.firstLaunchOpenChat = (Get.parameters['launchToChat'] ?? '') == 'true';
    scrollController = ScrollController(initialScrollOffset: 0);
    scrollController.addListener(() {
      Get.focusScope?.unfocus();
    });
    refreshController = EasyRefreshController(
      controlFinishLoad: true,
    );
    voicePlay = SessionVoicePlay();
    voicePlay.init();
    setAdUnlockTime();
    getSessionConfig();
    initSocket();
    //刷新余额
    UserService.to.getUserInfo();
    ReportUtil.reportViews(page: ReportUtil.chatwin, action: ReportUtil.view);
    //没有开启自动翻译且设备语言不是英语才能展示翻译按钮
    state.displayMsgTrans = (!UserService.to.isAutoTranslation || UserService.to.isVip == false) &&
        AmorTraService.locale != AmorTraService.fallbackLocale;
  }

  //获取会话配置 (视频、语音)
  getSessionConfig({bool updatePage = true, bool unlockRole = false}) async {
    Map<String, dynamic>? config = await SessionApis.getSessionConfig(
        sessionNo: state.sessionNo,
        cacheCallBack: (Map<String, dynamic>? cache) {
          if (unlockRole == false) {
            setSessionConfig(config: cache, cache: true, updatePage: updatePage, unlockRole: unlockRole);
          }
        });
    setSessionConfig(config: config, cache: false, updatePage: updatePage, unlockRole: unlockRole);
    getModelProfile();
  }

  setSessionConfig(
      {required Map<String, dynamic>? config,
      required bool cache,
      required bool updatePage,
      required bool unlockRole}) {
    if (config != null) {
      //默认发音人
      state.defaultVoice = config['voice'] ?? '';
      //对话标题
      state.sessionTitle = config['title'] ?? '';
      //对话头像
      state.sessionAvatarUrl = config['cover'];
      //是否点赞
      state.likeModel.value = config['hasLike'] ?? false;
      //背景图片
      state.bgImg.value = config['bgImg'] ?? '';
      //模型ID
      state.modelId = config['modelId'];
      //模型简介
      state.modelIntro = config['scenario'];
      //是否需要解锁角色
      state.lockModel = config['purview'] ?? 1; //默认不需要解锁
      //是否已解锁角色
      state.unlock.value = config['unlock'] ?? false; //默认未解锁
      //解锁需要的宝石数量
      state.unlockGems = config['unlockGems'] ?? 0;
      //nsfw开关状态
      state.contentCategorys = config['contentCategorys'] ?? [];
      //排队时间
      state.maxServiceWaitSeconds = config['queueTime'] ?? 500;
      //文字展示速度
      state.contentDisplayRate = config['showSpeed'] ?? 10;
      //等级信息
      state.sessionLevelInfo.value = config['userChatLevel'] ?? {};
      for (var element in state.contentCategorys) {
        if (element['selected'] == true) {
          state.contentCategory = element['contentCategory'];
          break;
        }
      }
      Loading.dismiss();
      //是否展示nsfw开关
      state.displayNsfw = (((config['contentCategorys'] ?? []) as List).isNotEmpty && AppService.audit == false);
      if (updatePage == false) {
        //已解锁角色 展示动画
        if (unlockRole == true && state.unlock.value == true) {
          state.showUnlockAnim.value = true;
          Future.delayed(const Duration(milliseconds: 1400), () => state.showUnlockAnim.value = false);
          AppService.sp.set(spAdUnlockRole, CommonUtil.currentTimeMillis());
        }
        return;
      }
      if (cache == false) {
        getmessageList();
        //每日弹出签到
        int curDate = CommonUtil.currentTimeMillis();
        int date = (Get.find<SPService>().get(spSigninDialog) ?? 0) as int;
        bool isSameDay = date == 0 ? false : CommonUtil.isSameDay(date, curDate);
        if (!isSameDay) {
          SignService.showSignDialog();
        }
      }
      if (state.unlock.value == false && state.lockModel == 2) {
        Analytics().logEvent(Analytics.viewLockedchar, screen: Analytics.pageChatwin, charID: state.modelId);
      }
      update();
    }
  }

  //连接socket
  initSocket() {
    socket.initSocket(sessionNum: state.sessionNo, type: 'chat');
  }

  //获取列表
  getmessageList({bool useCache = true}) async {
    List<SessionModel>? list = await SessionApis.getSessionMsgList(
      page: state.page,
      sessionType: 'MODEL',
      sessionNo: state.sessionNo,
      cacheCallBack: (cache) {
        if (useCache) {
          setList(list: cache, cache: true);
        }
      },
    );
    setList(list: list, cache: false);
  }

  setList({List<SessionModel>? list, required bool cache}) {
    if (list == null) {
      return;
    }
    if (state.page == 1) {
      state.messageList.assignAll(list);
      //添加简介
      if (state.modelIntro != null) {
        state.messageList.add(SessionModel(contentType: 'intro', content: state.modelIntro));
      }
      //AI生成提示
      state.messageList.add(SessionModel(contentType: 'aiGenerTip'));
    } else {
      state.messageList.addAll(list);
    }
    if ((list.isNotEmpty || state.page == 1)) {
      refreshListView(scrollToBottom: false, updatePage: false);
    }
    refreshController.finishLoad();
    if (cache) {
      return;
    }
    /*
    //自动播放第一条语音
    List msgAutoPlayList = (Get.find<SPService>().get(spMsgAutoPlay) ?? []) as List;
    if (state.showSetBaseInfo == false &&
            state.messageList.isNotEmpty &&
            msgAutoPlayList.contains(state.sessionNo) ==
                false /*&&
       state.unlock.value == false &&
        state.lockModel == 2*/
        ) {
      msgAutoPlayList.add(state.sessionNo);
      Get.find<SPService>().set(spMsgAutoPlay, jsonEncode(msgAutoPlayList));
      if (state.messageList.length == (state.modelIntro != null ? 3 : 2) &&
          state.messageList.first.contentType == 'sounds') {
        voicePlay.play(
            model: state.messageList.first, index: 0, sessionNo: state.sessionNo, userClick: true);
      }
    }
    */
  }

  //加载更多
  loadMoreRequest() async {
    if (state.messageList.length > 20) {
      state.page++;
      await getmessageList();
    } else {
      refreshController.finishLoad();
    }
  }

  //添加或删除等待回复消息
  setAwaitReply(bool show, {bool needRefresh = true}) {
    state.awaitReply.value = show;
    if (show) {
      state.messageList.insert(0, SessionModel(contentType: 'await'));
    } else {
      state.messageList.removeWhere((element) => element.contentType == 'await');
    }
    if (needRefresh) {
      refreshListView(scrollToBottom: true);
    }
  }

  //刷新列表元素
  refreshListView({String? id, bool scrollToBottom = false, bool updatePage = false}) {
    //刷新整个页面
    if (updatePage) {
      update();
    } else {
      update([id ?? 'listView']);
    }
    //滚动到底部
    if (scrollToBottom == true && scrollController.positions.isNotEmpty && state.page < 2) {
      scrollController.animateTo(.0, curve: Curves.ease, duration: const Duration(milliseconds: 200));
    }
  }

  //收到消息后文字转语音
  msgTextToVoice({required SessionModel model}) {
    if (model.unlock == 0) {
      return;
    }
    voicePlay.play(model: model, index: 0, sessionNo: state.sessionNo);
  }

  //播放语音
  playVoiceRequest({required int index}) async {
    //隐藏动作提示
    if (msgTipLogic.isShowTip) {
      msgTipLogic.hideTip();
      if (index > 0) {
        index = index - 1;
      }
    }
    SessionModel model = state.messageList.elementAt(index);
    if (model.contentType == 'audio') {
      Map? voiceData = jsonDecode(model.content ?? '');
      if (voiceData?['url'] == null) {
        return;
      }
      model.contentAnalysis = voiceData?['url'];
    }
    //生成或者下载中  无法点击
    if (voicePlay.underWayList.contains(model.msgId)) {
      Loading.toast('audio loading');
      return;
    }

    //停止播放
    if (SessionWidgetsController.to.state.voicesPlayIndex == index) {
      voicePlay.stopPlayVoice();
      return;
    }
    //设置为已点击状态
    Get.find<SPService>().set(spOntapPlayMsgVoice, true);
    voicePlay.play(model: model, index: index, sessionNo: state.sessionNo, userClick: true);
    ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.play);
  }

  //重新生成
  regenerate() async {
    //隐藏动作提示
    msgTipLogic.hideTip();
    //AI回复的最后一条消息
    if (state.messageList.elementAt(0).msgType == 2) {
      //正在播放这条消息
      if (SessionWidgetsController.to.state.voicesPlayIndex == 0) {
        voicePlay.stopPlayVoice();
      }
      String msgId = state.messageList.elementAt(0).msgId ?? '';
      if (msgId.isEmpty) {
        Loading.toast('error');
        return;
      }

      bool result = await SessionApis.chatRegenerate(sessionNo: state.sessionNo, msgId: msgId);
      if (result) {
        //生成语音时忽略重新生成的消息
        voicePlay.regenerateList.add(msgId);
        state.messageList.removeAt(0);
        setAwaitReply(true, needRefresh: false);
        refreshListView(scrollToBottom: true, updatePage: false);
        SessionQlite.removeChatMsg(msgId: msgId, sessionNo: state.sessionNo);
      }
    }
  }

  //取消字母输出定时器
  cancelTimer() {
    state.showTextMsgId = null;
    if (textOutputTimer != null) {
      textOutputTimer!.cancel();
      textOutputTimer = null;
    }
    // update(['stopGenerate']);
    state.awaitReply.refresh();
  }

//评分
  sessionScore({required String msgId, required int score, required String content}) async {
    if (state.msgRating.value != 0) {
      //每个消息只能评一次  收到消息后会重置这个值为0
      return;
    }
    //隐藏动作提示
    msgTipLogic.hideTip();
    state.msgRating.value = score;
    Future.delayed(const Duration(milliseconds: 1800), () {
      state.msgRatingShow.value = false;
      //低于3分弹出反馈
      if (score < 3 && state.modelId != -1 && Get.isRegistered<SessionPageController>() == true) {
        List reportList = ((Get.find<SPService>().get(spSessionScoreReport) ?? []) as List).toList();
        if (reportList.contains('${state.modelId}') == false) {
          reportList.add('${state.modelId}');
          Get.find<SPService>().set(spSessionScoreReport, reportList);
          Future.delayed(const Duration(milliseconds: 1000), () {
            SessionWidgetsController.to.bottomMenuReport(source: msgId, msgContent: content, type: 'rating');
          });
        }
      }
      if (score > 3 &&
          Get.find<SPService>().get(spChatShowScore) == null &&
          Get.isRegistered<SessionPageController>() == true) {
        showAppScore();
      }
    });

    bool result = await SessionApis.chatScore(msgId: msgId, score: score);
    if (result) {
      SessionModel model = state.messageList.first;
      model.score = score;
      SessionQlite.changeChatMsg(model: model, sessionNo: state.sessionNo);
      ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.rate, value: score.toString());
    }
  }

  //引导APP评分
  showAppScore() async {
    Get.find<SPService>().set(spChatShowScore, true);
    state.isShowAppScore = true;
    Analytics().logEvent(Analytics.view, screen: Analytics.pageRateguide);
    await Get.dialog(
      const RegisterFeedbackWidget(),
      useSafeArea: false,
    );
    state.isShowAppScore = false;
  }

  //签到
  signin(String signInType) async {
    Map? result = await CommonApis.signin(signInType: signInType);
    if (result != null && result['gems'] != null) {
      //刷新余额
      UserService.to.getUserInfo();
      GemsAnimation.show();
      Future.delayed(const Duration(milliseconds: 1800), () {
        Get.back();
      });
      SignService.signState.value = 1;
      SignService.signState.refresh();
    }
  }

  //宝石不足
  showGemsShort() async {
    ReportUtil.reportViews(page: ReportUtil.shortgem, action: ReportUtil.view);
    SignInitModel? model = await CommonApis.signinInit();
    if (model == null) {
      return;
    }
    await Get.dialog(SessionGemsShortWidget(
      model: model,
      callBack: ({required result}) {
        if (result == 0) {
          //会员直接领取签到宝石
          if (UserService.to.isVip) {
            if (model.status == 0) {
              signin('GENERAL');
            }
          } else {
            ReportUtil.reportViews(page: ReportUtil.shortgem, action: ReportUtil.go, value: ReportUtil.hot);
            Analytics().logEvent(Analytics.clickHot, screen: Analytics.pageOutofgem);
            Analytics().logEvent(
              Analytics.view,
              sourceEvent: Analytics.clickHot,
              sourceChar: state.modelId.toString(),
              sourceScreen: Analytics.pageOutofgem,
              screen: Analytics.pageHot,
            );
            Get.back();
            //非会员弹出购买页
            PurchaseSheet.show(page: ReportUtil.chatwin, modelId: state.modelId);
          }
        }
        //购买宝石
        if (result == 1) {
          ReportUtil.reportViews(page: ReportUtil.shortgem, action: ReportUtil.go, value: ReportUtil.gem);
          ReportUtil.reportPurchase(
              page: ReportUtil.gem, action: ReportUtil.view, value: 'from{${ReportUtil.shortgem}}');
          Analytics().logEvent(Analytics.clickHot, screen: Analytics.pageOutofgem);
          Analytics().logEvent(
            Analytics.view,
            sourceEvent: Analytics.clickHot,
            sourceChar: state.modelId.toString(),
            sourceScreen: Analytics.pageOutofgem,
            screen: Analytics.pageHot,
          );
          Get.back();
          PurchaseSheet.show(buyType: 1, page: ReportUtil.chatwin, modelId: state.modelId);
        }
        //做任务
        if (result == 2) {
          ReportUtil.reportViews(page: ReportUtil.shortgem, action: ReportUtil.go, value: ReportUtil.quests);
          Get.back();
          Get.offNamedUntil(Routes.task, ModalRoute.withName(Routes.tabs));
        }
        //普通会员签到
        if (result == 3 || result == 4) {
          if (model.status == 0) {
            signin(result == 3 ? 'GENERAL' : 'AD');
            if (result == 3) {
              Analytics().logEvent(Analytics.clickClaim, screen: Analytics.pageOutofgem);
            }
            ReportUtil.reportEvents(
                page: ReportUtil.shortgem, action: result == 3 ? ReportUtil.claim : ReportUtil.double);
          }
        }
      },
    ));
    ReportUtil.reportViews(page: ReportUtil.shortgem, action: ReportUtil.quit);
  }

  //任务完成弹窗 领取奖励
  taskClaimDialog({required Map data}) async {
    ReportUtil.reportEvents(
        page: ReportUtil.chatwin, action: ReportUtil.questPop, value: '${data['taskTitle']},${data['taskId']}');

    GemsDialog.receiveGemsDialog(
      gemsNum: data['gems'],
      subTitle: '${data['taskTitle']} Is Now Complete.',
      callBack: ({required int result}) async {
        if (result == 1) {
          /*
          ApplovinUtil.loadInterstitialAd(
            adUnitId: ApplovinUtil.interstitialAdUnitIdClone,
            callBack: (success) {
              if (success) {
                receiveReward(data['taskId'], 'AD');
              }
            },
          );
          */
        } else {
          receiveReward(data['taskId'], 'GENERAL');
        }
      },
    );
  }

  //领取奖励
  receiveReward(int taskId, String claimType) async {
    Map? success = await ProfileApis.taskClaim(id: taskId, claimType: claimType);
    Loading.dismiss();
    if (success != null && success.containsKey('gems')) {
      //刷新余额
      UserService.to.getUserInfo();
      GemsAnimation.show();
      Future.delayed(const Duration(milliseconds: 1800), () {
        Get.back();
      });
    }
  }

  //语音、视频通话
  showCall({required String type}) async {
    Analytics().logEvent(Analytics.ccall);
    if (UserService.to.isVip == false && type != 'video') {
      PurchaseSheet.show(page: ReportUtil.chatwin, modelId: state.modelId, source: Analytics.tpayvipcall);
      return;
    }
    if (await SttUtil().checkAvailable() == true && state.modelId != null) {
      Get.toNamed(Routes.voiceCall, parameters: {
        'voiceType': '0', //去电
        'callType': type, //视频、语音
        'modelId': state.modelId.toString(),
        'sessionNo': state.sessionNo,
        'avatarUrl': state.sessionAvatarUrl ?? '',
        'nickname': state.sessionTitle,
        'bgImgUrl': state.bgImg.value,
        'defaultVoice': state.defaultVoice,
      });
    }
  }

  //设置广告解锁倒计时
  setAdUnlockTime() async {
    if (AppService.sp.get(spAdUnlockRole) == null) {
      state.adUnlockCountDown.value = 0;
    } else {
      int differ = CommonUtil.timeDifference(CommonUtil.currentTimeMillis(), AppService.sp.get(spAdUnlockRole) as int,
          unit: 'seconds');
      state.adUnlockCountDown.value = differ > state.maxAdUnlockCountDown ? 0 : state.maxAdUnlockCountDown - differ;
    }
  }

  //替换会话
  replaceSession({required String sessionNo}) {
    state.sessionNo = sessionNo;
    state.page == 1;
    state.socketText.clear();
    state.messageList.clear();
    scrollController.jumpTo(0);
    Loading.show();
    setAdUnlockTime();
    getSessionConfig();
    initSocket();
    return;
  }

  // 判断是否支持视频通话
  getModelProfile() async {
    SessionApis.getModelProfile(
      modelId: '${state.modelId}',
      cacheCallBack: (cache) => setModelProfile(model: cache),
    ).then((value) => setModelProfile(model: value));
  }

  setModelProfile({AmorsFeedModel? model}) async {
    if (model != null && state.photos.isEmpty && AppService.audit == false) {
      state.photos.addAll(model.images ?? []);
      // 判断是否支持视频通话
      if (model.videoChat?['url'] != null) {
        if (model.videoChat?['coverGifUrl'] != null) {
          state.videoGifUrl = model.videoChat?['coverGifUrl'];
        }
        state.showVideoMenu.value = true;
      }
      SessionWidgetsController.to.state.sessionClothingId = model.clothingTypes ?? [];
    }
  }

  //查看相册图片
  showPhoto(int index) async {
    Map albumData = state.photos.elementAt(index);
    if (albumData['unlockGems'] == null) {
      return;
    }
    if (albumData['unlock'] != true && albumData['unlockGems'] > UserService.to.userinfo.value.gems) {
      PurchaseSheet.show(
          buyType: UserService.to.isVip ? 1 : 0,
          page: ReportUtil.chatwin,
          modelId: SessionPageController.to.state.modelId,
          source: Analytics.pageChatwin);
      return;
    }
    //解锁
    if (albumData['unlock'] == true ||
        await SessionApis.unlockPhoto(modelId: state.modelId!, imgId: albumData['id']) == true) {
      SessionWidgetsController.to.focusNode.unfocus();
      Get.dialog(ImageMessagePreviewWidget(picUrl: albumData['img']), useSafeArea: false);
      if (albumData['unlock'] == false) {
        //刷新余额
        UserService.to.getUserInfo();
        //刷新相册
        getModelProfile();
        albumData['unlock'] = true;
        state.photos.replaceRange(index, index + 1, [albumData]);
      }
    }
  }

  //升级后 通过配置接口刷新等级信息
  refreshSessionLevel() async {
    Map<String, dynamic>? config =
        await SessionApis.getSessionConfig(sessionNo: state.sessionNo, cacheCallBack: (Map<String, dynamic>? cache) {});
    if (config?['userChatLevel'] != null) {
      state.sessionLevelInfo.value = config?['userChatLevel'];
    }
  }

  // stopPlayVoice
  stopPlayVoice() {
    voicePlay.stopPlayVoice();
  }

  @override
  void onClose() {
    super.onClose();
    socket.disconnect();
    scrollController.dispose();
    refreshController.dispose();
    try {
      textOutputTimer!.cancel();
      textOutputTimer = null;
      // ignore: empty_catches
    } catch (e) {}
    voicePlay.dispose();
    ReportUtil.reportViews(page: ReportUtil.chatwin, action: ReportUtil.quit);
    debugPrint('SessionCtl销毁');
    if (Get.isRegistered<InnerPageController>() == true) {
      InnerPageController.to.refreshIndexTabbarList(index: 0);
    }
  }
}
