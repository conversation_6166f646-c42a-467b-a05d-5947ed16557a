import 'dart:ui';

import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/special_text_span/special_text_span_builder.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/logic/session_socket_send.dart';
import 'package:amor_app/pages/session/widgets/controller/controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';

import 'session_bottom_menu.dart';
import 'session_input_box.dart';

class SessionBottom extends StatefulWidget {
  final String sessionTitle;
  const SessionBottom({super.key, required this.sessionTitle});

  @override
  State<SessionBottom> createState() => _SessionBottomState();
}

class _SessionBottomState extends State<SessionBottom> {
  late SessionWidgetsController controller;
  @override
  void initState() {
    super.initState();
    controller = SessionWidgetsController.to;
  }

  @override
  Widget build(BuildContext context) {
    //监听键盘弹出
    return KeyboardVisibilityBuilder(
      builder: (p0, isKeyboardVisible) {
        return bottomContent(isKeyboardVisible: isKeyboardVisible);
      },
    );
  }

  Widget bottomContent({required bool isKeyboardVisible}) {
    if (Get.isRegistered<SessionPageController>() == false) {
      return Container();
    }
    return ClipRRect(
      borderRadius: BorderRadius.only(topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r)),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
        child: Container(
          padding: EdgeInsets.only(top: 12.w, bottom: 8.w + (isKeyboardVisible ? 0 : CommonUtil.bottomBarHeight())),
          decoration: BoxDecoration(
            // borderRadius:
            //     BorderRadius.only(topLeft: Radius.circular(16.r), topRight: Radius.circular(16.r)),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColor.colorsUtil('#27292D').withValues(alpha: 0.8),
                AppColor.colorsUtil('#111214').withValues(alpha: 0.8)
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            spacing: 8,
            children: [
              if (AppService.audit == false)
                Row(
                  spacing: 8,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(width: 4),
                    InkWell(
                      onTap: () => SessionWidgetsController.to.giftAtion(),
                      child: Container(
                        height: 24.w,
                        padding: EdgeInsets.symmetric(horizontal: 18.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24.w),
                          color: CommonUtil.colorsUtil('#FDCB7C', alpha: 0.2),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Image.asset(Assets.assetsImagesChatBottomGift, width: 12.w),
                            2.horizontalSpace,
                            Text(
                              'Gifts'.tr,
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w400,
                                color: CommonUtil.colorsUtil('#FFCA75'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (AppService.audit == false && SessionWidgetsController.to.state.showUndress)
                      GestureDetector(
                        onTap: () {
                          var modelId = SessionPageController.to.state.modelId ?? 0;
                          var avatar = SessionPageController.to.state.sessionAvatarUrl ?? '';
                          Get.toNamed(Routes.undress, arguments: {'modelId': modelId, 'avatar': avatar});
                        },
                        child: SizedBox(
                          height: 24.w,
                          child: Image.asset(Assets.assetsImagesChatUndressBtn),
                        ),
                      ),
                    SizedBox(width: 4)
                  ],
                ),
              Row(
                children: [
                  //展开收起底部菜单
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      controller.setBottomMwnu();
                    },
                    child: Padding(
                      padding: REdgeInsets.symmetric(horizontal: 12),
                      child: Image.asset(Assets.assetsImagesSessionBottomMenuShow, width: 42.w, height: 42.w),
                    ),
                  ),
                  Expanded(
                    child: SessionInputBox(
                      hintText: 'chat hintText'.trArgs([(widget.sessionTitle)]),
                      autofocus: false,
                      onEditingComplete: () {
                        SessionWidgetsController.to.inputText = '';
                        if (controller.chatInputTextFieldCtl.text.isNotEmpty) {
                          if (SessionSocketSend.sendMessage(
                                  content: controller.chatInputTextFieldCtl.text,
                                  type: 1,
                                  requestId: '${CommonUtil.currentTimeMillis()}') ==
                              true) {
                            controller.chatInputTextFieldCtl.text = '';
                          }
                        }
                      },
                      onSubmitted: (String _) {},
                      specialTextSpanBuilder: AppSpecialTextSpan(),
                      onChanged: (value) {
                        //输入星号
                        if (value.endsWith('*')) {
                          if (controller.insetSpecialText() == true) {
                            return;
                          }
                        }
                        SessionWidgetsController.to.inputText = value;
                      },
                      controller: controller.chatInputTextFieldCtl,
                      focusNode: controller.focusNode,
                    ),
                  ),
                  10.horizontalSpace,
                ],
              ),
              //底部菜单
              const ChatBottomMenu(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
