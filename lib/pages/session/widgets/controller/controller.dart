import 'dart:convert';

import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/session_model/session_model.dart';
import 'package:amor_app/common/models/session_model/session_report_model.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/db_util/session_entity.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:amor_app/pages/session/logic/session_sqlite.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/image_message_preview.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/level_dialog_widget.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/level_reward_dialog_widget.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/report_bottom_sheet.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/session_nsfw.dart';
import 'package:amor_app/pages/session/widgets/dialog_widgets/undress/widget.dart';
import 'package:amor_app/pages/session/widgets/gift_widget/clothing_load.dart';
import 'package:amor_app/pages/session/widgets/gift_widget/widget.dart';
import 'package:amor_app/pages/session/widgets/session_suspend/session_msg_edit.dart';
import 'package:amor_app/pages/session/widgets/video/controller.dart';
import 'package:amor_app/pages/session/widgets/video/video_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

import '../dialog_widgets/nsfw_switch_dialog.dart';
import '../dialog_widgets/reset_dialog.dart';
import 'state.dart';

class SessionWidgetsController extends GetxController {
  static SessionWidgetsController get to => Get.find();
  final FocusNode focusNode = FocusNode();
  final state = SessionWidgetsState();
  late TextEditingController chatInputTextFieldCtl;
  late TextEditingController reportTextFieldCtl;

  //监听键盘
  // late StreamSubscription<bool> keyboardSubscription;
  String inputText = '';
  @override
  void onInit() {
    super.onInit();
    chatInputTextFieldCtl = TextEditingController();
    reportTextFieldCtl = TextEditingController();
    setInputController();
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        if (state.bottomMenuShow.value) {
          state.bottomMenuShow.toggle();
        }
      }
    });
    chatInputTextFieldCtl.addListener(() {
      if (chatInputTextFieldCtl.text.isNotEmpty) {
        //隐藏动作提示
        SessionPageController.to.msgTipLogic.hideTip();
      }
    });
    state.showUndress = (AppService.sp.get(spChatShowUndress) == true && AppService.audit == false);
    //监听键盘
    // var keyboardVisibilityController = KeyboardVisibilityController();
    // keyboardSubscription = keyboardVisibilityController.onChange.listen((bool visible) {});
  }

  //设置textfieldCtl
  setInputController() async {
    //填充发送失败的消息
    List<Map<String, dynamic>> cacheData =
        await SessionEntity.instan().find(where: {'sessionNo': SessionPageController.to.state.sessionNo});
    String text = '';
    if (cacheData.isNotEmpty) {
      text = cacheData.last['content'];
    }
    chatInputTextFieldCtl.text = text;
  }

  //重置播放语音消息下标
  resetPlayIndex() {
    state.voicesPlayIndex = 100000000;
  }

  //输入星号时，自动添加尾部星号，输入内容保持在两个星号之间
  bool? insetSpecialText() {
    if (chatInputTextFieldCtl.value.text == inputText && inputText.endsWith('*')) {
      chatInputTextFieldCtl.text = inputText.substring(0, inputText.length - 1);
      inputText = chatInputTextFieldCtl.value.text;
      return true;
    }
    if (chatInputTextFieldCtl.value.text.length > inputText.length) {
      if (chatInputTextFieldCtl.value.text.length == chatInputTextFieldCtl.value.selection.end &&
          countOccurrences(chatInputTextFieldCtl.value.text, '*').isOdd) {
        String newText = '${chatInputTextFieldCtl.text}*';
        chatInputTextFieldCtl.value = TextEditingValue(
          text: '${chatInputTextFieldCtl.text}*',
          selection: TextSelection.collapsed(offset: newText.length - 1),
        );
      }
    }
    return null;
  }

  int countOccurrences(String text, String target) {
    int count = 0;
    for (int i = 0; i < text.length; i++) {
      if (text[i] == target) {
        count++;
      }
    }
    return count;
  }

  //展开、收起底部菜单
  setBottomMwnu({bool clickBlank = false}) async {
    //隐藏动作提示 （点击空白处只收起键盘，不隐藏动作提示）
    if (clickBlank == false) {
      SessionPageController.to.msgTipLogic.hideTip();
    }
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    state.bottomMenuShow.toggle();
    focusNode.unfocus();
    SessionPageController.to.refreshListView();
    ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.extra);
  }

  //重置
  bottomMenuReset() async {
    setBottomMwnu();
    int? result = await Get.dialog(const ResetDialogWidget());
    if (result == 1) {
      bool result = await SessionApis.sessionReset(sessionNo: SessionPageController.to.state.sessionNo);
      if (result) {
        SessionPageController.to.state.page = 1;
        SessionPageController.to.getmessageList(useCache: false);
        ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.reset);
      }
    }
  }

  //背景
  bottomMenuCards() {
    setBottomMwnu();
    ReportUtil.reportViews(page: ReportUtil.chatwin, action: ReportUtil.go, value: ReportUtil.card);
    Future.delayed(const Duration(milliseconds: 200), () {
      Get.toNamed(Routes.sessionSetBg, parameters: {'modelId': '${SessionPageController.to.state.modelId}'});
    });
    ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.actionCards);
  }

  //分享
  bottomMenuShare() async {
    setBottomMwnu();
    Map? shareParam = await CommonApis.commonShare(modelId: SessionPageController.to.state.modelId);
    if (shareParam != null) {
      Share.share(shareParam['text']);
      ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.share);
    }
  }

  //反馈
  bottomMenuReport({required String source, required String type, String? msgContent, String? modelName}) async {
    if (type == 'direct') {
      setBottomMwnu();
    }
    Duration duration = Duration.zero;
    if (focusNode.hasFocus) {
      duration = const Duration(milliseconds: 400);
      focusNode.unfocus();
    }
    List<ReportInfoModel> reportInfoList = await SessionApis.reportInfo();
    if (reportInfoList.isNotEmpty) {
      Future.delayed(duration, () {
        showReport(
          reportInfoList: reportInfoList,
          source: source,
          msgContent: msgContent,
          modelName: modelName,
          type: type,
        );
      });
      ReportUtil.reportEvents(page: ReportUtil.chatwin, action: ReportUtil.actionReport);
    }
  }

  //反馈
  showReport(
      {required List<ReportInfoModel> reportInfoList,
      required String source,
      required String type,
      String? msgContent,
      String? modelName}) async {
    state.selectedReport.clear();
    reportTextFieldCtl.clear();
    int? result = await Get.bottomSheet(
      AnimatedPadding(
        padding: MediaQuery.of(Get.context!).viewInsets,
        duration: Duration.zero,
        child: ReportBottomSheet(reportInfoList: reportInfoList),
      ),
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
    );
    if (result == 1 && (state.selectedReport.isNotEmpty || reportTextFieldCtl.text.isNotEmpty)) {
      SessionApis.reportPromble(
          ids: state.selectedReport,
          source: source,
          type: type,
          value: type == 'rating' ? msgContent ?? '' : modelName ?? '',
          text: reportTextFieldCtl.text);
    }
  }

  //选择反馈
  selectedReport(ReportInfoModelItem model) {
    if (state.selectedReport.contains(model.id)) {
      state.selectedReport.remove(model.id);
    } else {
      state.selectedReport.add(model.id);
    }
    Get.focusScope?.unfocus();
  }

//展示nsfw切换组件
  showNsfwSwitch() async {
    focusNode.unfocus();
    await Future.delayed(Duration(milliseconds: focusNode.hasFocus ? 400 : 0));
    final RenderBox? renderBox = state.nsfwSwitchWidgetKey.currentContext?.findRenderObject() as RenderBox?;
    Offset? position;
    if (renderBox != null) {
      position = renderBox.localToGlobal(Offset.zero);
    }
    int? result = await Get.dialog(SessionNsfwSwitch(position: position ?? Offset(112.w, 1.sh - 70.h)),
        useSafeArea: false, barrierColor: Colors.transparent, transitionDuration: const Duration(milliseconds: 50));
    if (result != null) {
      nsfwSwitch(SessionPageController.to.state.contentCategorys[result]['contentCategory']);
    }
  }

//nsfw开关
  nsfwSwitch(String type) async {
    SessionPageController.to.msgTipLogic.hideTip();
    if (SessionPageController.to.state.contentCategory == type) {
      return;
    }
    int? result = await Get.dialog(
      ModelSwitchDialogWidget(contentCategory: type),
      transitionDuration: const Duration(milliseconds: 100),
    );
    if (result == 1) {
      SessionPageController.to.state.contentCategory = type;
      SessionPageController.to.refreshListView(updatePage: true);
      bool success = await SessionApis.nsfwSwitch(
          sessionNo: SessionPageController.to.state.sessionNo,
          modelId: SessionPageController.to.state.modelId,
          contentCategory: SessionPageController.to.state.contentCategory);
      if (success) {
        SessionPageController.to.getSessionConfig(updatePage: false);
        ReportUtil.reportEvents(
            page: ReportUtil.chatwin, action: ReportUtil.select, value: SessionPageController.to.state.contentCategory);
      }
    }
  }

//编辑消息
  editMessage({required SessionModel model, required int listIndex}) async {
    SessionPageController pageController = SessionPageController.to;
    //正在输出消息、等待回复时不能编辑消息
    if (pageController.textOutputTimer != null || pageController.state.awaitReply.value == true) {
      return;
    }
    //隐藏动作提示
    pageController.msgTipLogic.hideTip();
    focusNode.unfocus();
    pageController.voicePlay.stopPlayVoice();
    pageController.refreshListView(updatePage: true);
    state.editMessageText = '';
    int? type = await Get.bottomSheet(
      MessageEditWidget(msgType: model.msgType ?? 2, content: model.content ?? ''),
      ignoreSafeArea: true,
      isScrollControlled: false,
      enableDrag: false,
      isDismissible: false,
    );
    // Future.delayed(const Duration(milliseconds: 300), () {
    //   pageController.refreshListView(updatePage: true);
    // });
    if (state.editMessageText.isEmpty || type == null || state.editMessageText == model.content) {
      return;
    }
    bool result = await SessionApis.updateMsg(
        msgId: model.msgId ?? '', content: state.editMessageText, contentType: type == 1 ? 'sounds' : 'text');
    if (result != true) {
      return;
    }
    if (type == 0 || type == 1) {
      int index = pageController.state.messageList.indexOf(model);
      model.content = state.editMessageText;
      if (type == 0) {
        model.contentType = 'text';
      } else {
        model.contentType = 'sounds';
        model.audioBuffer = null;
        model.contentAnalysis = null;
        int duration = CommonUtil.extractEmojis(model.content ?? '').length ~/ 9;
        model.duration = duration > 0 ? duration : 1;
      }
      pageController.state.messageList.replaceRange(index, index + 1, [model]);
      pageController.refreshListView(id: model.msgId);
      //刷新数据库
      SessionQlite.changeChatMsg(model: model, sessionNo: pageController.state.sessionNo);
    }
    if (type == 2) {
      model.content = state.editMessageText;
      SessionQlite.removeChatMsg(
          msgId: pageController.state.messageList.elementAt(0).msgId, sessionNo: pageController.state.sessionNo);
      pageController.state.messageList.removeAt(0);
      pageController.setAwaitReply(true, needRefresh: false);
      pageController.refreshListView(scrollToBottom: true);
      //刷新数据库
      SessionQlite.changeChatMsg(model: model, sessionNo: pageController.state.sessionNo);
    }
  }

  //查看图片
  showImage({required SessionModel model}) async {
    if (UserService.to.isVip == false && model.contentType == 'image') {
      Analytics().logEvent(Analytics.clickLockedpic,
          screen: Analytics.pageChatwin, charID: SessionPageController.to.state.modelId);
      Analytics().logEvent(
        Analytics.view,
        sourceEvent: Analytics.clickLockedpic,
        sourceChar: SessionPageController.to.state.modelId.toString(),
        sourceScreen: Analytics.pageChatwin,
        screen: Analytics.pageHot,
      );
      Analytics().logEvent(Analytics.clockpic);
      Future.delayed(Duration(milliseconds: focusNode.hasFocus == true ? 500 : 0), () {
        PurchaseSheet.show(
            page: ReportUtil.chatwin,
            modelId: SessionPageController.to.state.modelId,
            source: Analytics.tpaygemslockpic);
      });
      if (focusNode.hasFocus == true) {
        focusNode.unfocus();
      }
      return;
    }
    Get.dialog(
        ImageMessagePreviewWidget(
            picUrl: model.contentType == 'image' ? null : model.giftImg,
            chatMessageModel: model.contentType == 'image' ? model : null),
        useSafeArea: false);
  }

//将图片设置为聊天背景
  setBackgroundImage({required SessionModel model}) async {
    SessionPageController.to.state.bgImg.value = model.content ?? '';
    if (SessionPageController.to.state.modelId != null) {
      bool result = await SessionApis.bgActivate(modelId: SessionPageController.to.state.modelId!, msgId: model.msgId);
      if (result == true) {
        SessionPageController.to.getSessionConfig(updatePage: false);
        Get.back();
      }
    }
  }

//展示undress弹窗
  showUndress() async {
    if (SessionPageController.to.state.modelId != null) {
      focusNode.unfocus();
      Future.delayed(Duration(milliseconds: focusNode.hasFocus ? 400 : 0), () {
        UndressDialog.show(
          modelId: SessionPageController.to.state.modelId ?? 0,
          avatar: SessionPageController.to.state.sessionAvatarUrl ?? '',
        );
      });
      Analytics().logEvent(Analytics.tunpop);
    }
  }

  //播放视频
  playVideo({required SessionModel model}) async {
    focusNode.unfocus();
    if (UserService.to.isVip != true) {
      Analytics().logEvent(Analytics.clockvideo);
      Future.delayed(Duration(milliseconds: focusNode.hasFocus ? 400 : 0), () {
        PurchaseSheet.show(
            page: ReportUtil.chatwin,
            modelId: SessionPageController.to.state.modelId,
            source: Analytics.tpaygemslockvideo);
      });
      return;
    }
    Map? data = jsonDecode(model.content ?? '');
    if (data?['url'] != null) {
      Get.lazyPut(() => SessionVideoPlayController(videoUrl: data!['url']));
      await Get.dialog(const SessionVideoPlayPage(), useSafeArea: false, barrierDismissible: false);
      Get.delete<SessionVideoPlayController>(force: true);
    }
  }

  //展示等级信息
  showSessionLevelInfo() async {
    if (AppService.configModel.chatLevelConf != null) {
      List list = jsonDecode(AppService.configModel.chatLevelConf!) ?? [];
      if (list.isNotEmpty) {
        Get.dialog(LevelDialogWidget(chatLevelConf: list));
      }
    }
  }

  //等级奖励
  void showLevelReward(int gems) async {
    focusNode.unfocus();
    bool isShow = true;
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (isShow) Get.back();
    });
    //刷新余额
    UserService.to.getUserInfo();
    await Get.dialog(SessionLevelRewardWidget(gems: gems), barrierColor: Colors.transparent);
    isShow = false;
    GemsAnimation.show(source: 'assets/lottie/session_upgrade.json');
  }

  //翻译
  messageTranslate({required SessionModel model, required int index}) async {
    focusNode.unfocus();
    // AnalyticsUtils().logEvent(AnalyticsUtils.ctrans);
    // 处理非会员一分钟内连续点击3次的逻辑
    if (UserService.to.isVip == false) {
      final now = DateTime.now();
      if (state.transLastClickTime != null && now.difference(state.transLastClickTime!).inMinutes < 1) {
        state.transClickCount++;
      } else {
        state.transClickCount = 1;
      }
      state.transLastClickTime = now;
      if (state.transClickCount >= 3 && AppService.sp.get(spKeyMsgAutoTranslateTip) == null) {
        bool? confirm = await Get.dialog(
          CustomDialogWidget(
            title: 'Translation'.tr,
            subTitle: 'Enable automatic translation?'.tr,
            confirmTitle: 'Sure'.tr,
            cancelTitle: 'Cancel'.tr,
            confirmColor: '#00B0F9',
          ),
        );

        if (confirm == true) {
          PurchaseSheet.show(page: ReportUtil.chatwin, modelId: SessionPageController.to.state.modelId);
        }
        AppService.sp.set(spKeyMsgAutoTranslateTip, true);
        return;
      }
    }
    // 翻译
    if (model.translateContent == null) {
      String? result = await SessionApis.sessionMessageTranslate(
          tarLang: (Get.deviceLocale ?? const Locale('en', 'US')).toString(),
          souLang: 'en_US',
          text: model.content ?? '');
      if (result == null) {
        Loading.toast('Translation failed');
        return;
      }
      model.translateContent = result;
      //修改服务器消息
      SessionApis.setMsgVoice(msgId: model.msgId ?? '', translateMessage: model.translateContent ?? '');
    }
    //刷新消息列表
    model.showTransContent = true;
    model = SessionModel.fromJson(model.toJson());
    SessionPageController.to.state.messageList.replaceRange(index, index + 1, [model]);
    SessionPageController.to.refreshListView(id: model.msgId);
    //刷新数据库
    SessionQlite.changeChatMsg(model: model, sessionNo: SessionPageController.to.state.sessionNo);
  }

  //展示原文
  void showOriginalMessage({required SessionModel model, required int index}) {
    model.showTransContent = false;
    SessionPageController.to.state.messageList.replaceRange(index, index + 1, [model]);
    SessionPageController.to.refreshListView(id: model.msgId);
  }

  //送礼弹窗
  Future<void> giftAtion() async {
    focusNode.unfocus();
    await Future.delayed(Duration(milliseconds: focusNode.hasFocus ? 400 : 0));
    if (SessionPageController.to.state.modelId != null) {
      List<SessionModel>? result = await Get.bottomSheet(
        SeeionGiftWidget(
            modelId: SessionPageController.to.state.modelId!,
            sessionNo: SessionPageController.to.state.sessionNo,
            clothingIds: state.sessionClothingId),
        isScrollControlled: true,
        ignoreSafeArea: true,
      );
      if (result != null && result.isNotEmpty) {
        if (result.first.contentType == 'clothe') {
          SessionModel? model = result.firstWhereOrNull((element) => element.msgType == 2);
          if (model?.giftImg != null) {
            showClothingAwait(imgUrl: model!.giftImg!);
          }
        }
        SessionPageController.to.state.messageList.insertAll(0, result.reversed);
        SessionPageController.to.refreshListView(scrollToBottom: true);
        for (var element in result) {
          SessionQlite.insertChatMsg(model: element, sessionNo: SessionPageController.to.state.sessionNo);
        }
      }
    }
  }

  //赠送服装后等待展示图片
  Future<void> showClothingAwait({required String imgUrl}) async {
    Get.dialog(ClothingLoad(sessionTitle: SessionPageController.to.state.sessionTitle, picUrl: imgUrl),
        useSafeArea: false, barrierDismissible: false);
  }

  //展示礼物图片
  Future<void> showGiftImage(String imageUrl) async {
    Get.dialog(ImageMessagePreviewWidget(picUrl: imageUrl), useSafeArea: false);
  }

  @override
  void dispose() {
    super.dispose();
    focusNode.dispose();
    chatInputTextFieldCtl.dispose();
    reportTextFieldCtl.dispose();
  }
}
