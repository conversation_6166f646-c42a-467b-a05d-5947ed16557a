import 'package:amor_app/common/models/clone_model/step_content_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/klone/sub_pages/klone_step_two/controller.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class StepContentTextWidget extends StatelessWidget {
  final CloneStepContentModel contentModel;
  const StepContentTextWidget({super.key, required this.contentModel});
  @override
  Widget build(BuildContext context) {
    // bool showVoice = contentModel.type == 3 &&
    //     KloneStepTwoPageController.to.state.voiceParam.isNotEmpty &&
    //     contentModel.source == 0 &&
    //     contentModel.msgId != null;
    // 2025 年 7 月 25 日 01:00:57 语音服务已经暂停，隐藏按钮
    bool showVoice = false;

    return Stack(
      children: [
        Row(
          mainAxisAlignment:
              contentModel.source == 1 ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: contentModel.source == 1 ? rowChildren().reversed.toList() : rowChildren(),
        ),
        //播放按钮
        if (showVoice)
          Positioned(
            bottom: 0,
            left: 20.w,
            child: playButtonWidget(),
          ),
      ],
    );
  }

  //内容组件列表
  List<Widget> rowChildren() {
    return [
      _contentContainer(
        child: Stack(
          children: [
            Container(
              margin: contentModel.type == 4 ? EdgeInsets.only(right: 26.w) : null,
              child: EasyRichText(
                contentModel.content ?? '',
                textAlign: TextAlign.start,
                defaultStyle: TextStyle(
                  fontSize: 15.sp,
                  height: 20 / 15,
                  color: contentModel.contentColor ??
                      (contentModel.source == 1
                          ? AppColor.colorsUtil('#333333')
                          : AppColor.colorsUtil('#F0BE72')),
                  fontFamily: fontBeVietnamPro,
                  fontStyle: contentModel.source == 1 ? FontStyle.italic : FontStyle.normal,
                ),
                patternList: contentModel.type == 3 && contentModel.specialText != null
                    ? [
                        EasyRichTextPattern(
                          targetString: contentModel.specialText,
                          style: TextStyle(
                            color: contentModel.specialColor ?? AppColor.colorsUtil('#F0BE72'),
                          ),
                        ),
                      ]
                    : null,
              ),
            ),
            //可编辑文本
            if (contentModel.type == 4)
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: InkWell(
                  onTap: () {
                    if (KloneStepTwoPageController.to.state.recordState.value == 1) {
                      return;
                    }
                    KloneStepTwoPageController.to.editContentText(contentModel);
                  },
                  child: Padding(
                    padding: EdgeInsets.only(left: 10.w),
                    child: Image.asset(
                      Assets.assetsImagesCloneVoiceTextEdit,
                      width: 16.w,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
      if (contentModel.showRedo == true)
        InkWell(
          onTap: () {
            KloneStepTwoPageController.to.fineTuneLogic.textWidgetRedo(contentModel);
          },
          child: Padding(
            padding: EdgeInsets.only(left: 10.w, bottom: 10.w, right: 10.w),
            child: Column(
              children: [
                Image.asset(
                  Assets.assetsImagesChatMsgRefresh,
                  width: 20.w,
                ),
                if (contentModel.source == 0)
                  Text(
                    'Redo'.tr,
                    style: TextStyle(fontSize: 10.sp, color: Colors.white),
                  ),
              ],
            ),
          ),
        ),
    ];
  }

  //播放按钮
  Widget playButtonWidget() {
    return InkWell(
      onTap: () => KloneStepTwoPageController.to.playQuestionVoice(contentModel),
      child: Container(
        height: 25.w,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4.r),
            topRight: Radius.circular(25.w / 2),
            bottomLeft: Radius.circular(25.w / 2),
            bottomRight: Radius.circular(25.w / 2),
          ),
          gradient: LinearGradient(
            colors: [AppColor.colorsUtil('#EAC282'), AppColor.colorsUtil('#C69A51')],
          ),
        ),
        child: Row(
          children: [
            //生成或下载中
            KloneStepTwoPageController.to.questionVoicePlay.underWayList
                    .contains(contentModel.msgId)
                ? SizedBox(
                    height: 13.w,
                    width: 13.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: const AlwaysStoppedAnimation<Color>(AppColor.primaryText),
                      backgroundColor: AppColor.primaryText.withValues(alpha: 0.2),
                    ),
                  )
                : KloneStepTwoPageController.to.state.playingQuestion == contentModel.msgId
                    ? SizedBox(
                        height: 13.w,
                        width: 17.w,
                        child: Lottie.asset('assets/lottie/session_voice_play.json', animate: true),
                      )
                    : Image.asset(
                        Assets.assetsImagesSessionPagePlay,
                        width: 12.w,
                        height: 12.w,
                      ),

            KloneStepTwoPageController.to.questionVoicePlay.underWayList
                        .contains(contentModel.msgId) ||
                    contentModel.duration == null
                ? 0.horizontalSpace
                : 8.horizontalSpace,
            Text(
              KloneStepTwoPageController.to.questionVoicePlay.underWayList
                          .contains(contentModel.msgId) ||
                      contentModel.duration == null
                  ? ''
                  : '${contentModel.duration}″',
              style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColor.primaryText,
                  height: 1.2,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }

  //容器
  Widget _contentContainer({required Widget child}) {
    bool showVoice = contentModel.type == 3 &&
        KloneStepTwoPageController.to.state.voiceParam.isNotEmpty &&
        contentModel.source == 0 &&
        contentModel.msgId != null;
    return Opacity(
      opacity: 0.94,
      child: Container(
        constraints: BoxConstraints(maxWidth: 1.sw - 95.w),
        decoration: BoxDecoration(
          color: contentModel.source == 1
              ? Colors.white.withValues(alpha: 0.94)
              : AppColor.colorsUtil('#323235'),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(contentModel.source == 1 ? 16.r : 6.r),
            topRight: Radius.circular(contentModel.source == 1 ? 6.r : 16.r),
            bottomLeft: Radius.circular(16.r),
            bottomRight: Radius.circular(16.r),
          ),
        ),
        padding: EdgeInsets.fromLTRB(20.w, 15.w, 20.w, showVoice ? 20.w : 15.w),
        margin: showVoice ? EdgeInsets.only(bottom: 25.w / 2) : EdgeInsets.zero,
        child: child,
      ),
    );
  }
}
