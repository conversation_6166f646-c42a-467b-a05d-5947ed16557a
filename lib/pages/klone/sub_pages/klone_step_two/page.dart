import 'dart:ui';

import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';
import 'widgets/step_content.dart';

class KloneStepTwoPage extends GetView<KloneStepTwoPageController> {
  const KloneStepTwoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        scrolledUnderElevation: 0,
        titleSpacing: 0,
        backgroundColor: Colors.transparent,
        title: _title(),
        leading: CustomBackButton(back: () async {
          if (controller.state.cloneCompleted.value == true) {
            return;
          }
          ReportUtil.reportViews(
              page: controller.state.cloneDone.value ? ReportUtil.cloneFT : ReportUtil.cloneVT,
              action: ReportUtil.quit);
          await controller.saveProgress();
          Get.until((route) => route.settings.name!.contains(Routes.tabs));
        }),
        // actions: _actions(),
      ),
      backgroundColor: AppColor.mainBg,
      body: Stack(
        children: [
          Positioned.fill(
            child: Obx(
              () => controller.state.bgImageUrl.value.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: controller.state.bgImageUrl.value,
                      width: 1.sw,
                      height: 1.sh,
                      fit: BoxFit.cover,
                    )
                  : Container(),
            ),
          ),
          //消息列表
          Positioned.fill(
            child: Column(
              children: [
                SizedBox(
                  height: CommonUtil.statusBarHeight(context) + CommonUtil.appBarHeight(),
                ),
                Expanded(
                  child: Container(
                    // blendMode: BlendMode.dstIn,
                    // shaderCallback: (rect) {
                    //   return const LinearGradient(
                    //     colors: [Colors.transparent, Colors.white],
                    //     begin: Alignment.topCenter,
                    //     end: Alignment.bottomCenter,
                    //     stops: [0, 0.1],
                    //   ).createShader(rect);
                    // },
                    child: GetBuilder<KloneStepTwoPageController>(
                        id: 'cloneListWidget',
                        builder: (controller) {
                          return Column(
                            children: [
                              const Spacer(),
                              Container(
                                constraints: BoxConstraints(
                                    maxHeight: 1.sh -
                                        (CommonUtil.statusBarHeight(context) +
                                            CommonUtil.appBarHeight())),
                                child: NotificationListener<OverscrollIndicatorNotification>(
                                  onNotification: (OverscrollIndicatorNotification? overscroll) {
                                    overscroll!.disallowIndicator();
                                    return true;
                                  },
                                  child: ListView.builder(
                                    padding: EdgeInsets.fromLTRB(
                                        0, 20.w, 0, 10.w + CommonUtil.bottomBarHeight()),
                                    // reverse: true,
                                    shrinkWrap: true,
                                    physics: const ClampingScrollPhysics(),
                                    controller: controller.scrollController,
                                    itemCount: controller.state.messageList.length,
                                    itemBuilder: (BuildContext context, int index) {
                                      return GetBuilder<KloneStepTwoPageController>(
                                        id: controller.state.messageList.elementAt(index).msgId,
                                        builder: (controller) {
                                          return CloneStepContent(
                                            contentModel:
                                                controller.state.messageList.elementAt(index),
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ],
                          );
                        }),
                  ),
                ),
              ],
            ),
          ),
          //顶部标题
          Positioned(
            top: 55.h + CommonUtil.statusBarHeight(context),
            left: 0,
            right: 0,
            child: Center(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(40.w / 2),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10.0, sigmaY: 10.0),
                  child: Container(
                    width: 246.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: AppColor.colorsUtil('#3C3C3D').withValues(alpha: 0.5),
                    ),
                    alignment: Alignment.center,
                    child: Obx(
                      () => Text(
                        controller.state.cloneDone.value == true ? 'Fine-tuning'.tr : 'Voice Traning'.tr,
                        style: TextStyle(
                            fontSize: 15.sp,
                            color: AppColor.colorsUtil('#F0BE72'),
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w800),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          //克隆完成后用作遮挡 使不能有其他操作
          Obx(
            () => controller.state.cloneCompleted.value == false
                ? Container()
                : Positioned.fill(
                    child: Container(color: Colors.transparent),
                  ),
          ),
        ],
      ),
    );
  }

  //模型名称
  Widget _title() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Obx(() => controller.state.avatarUrl.value.isNotEmpty
            ? Container(
                width: 36.w,
                height: 36.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(36.w / 2),
                  boxShadow: [
                    BoxShadow(blurRadius: 1.0, color: Colors.black.withValues(alpha: 0.5))
                  ],
                ),
                clipBehavior: Clip.antiAlias,
                child: CachedNetworkImage(
                    imageUrl: controller.state.avatarUrl.value, fit: BoxFit.cover),
              )
            : Container()),
        5.horizontalSpace,
        Container(
          constraints: BoxConstraints(maxWidth: 150.w),
          child: Obx(
            () => Text(
              controller.state.name.value,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: TextStyle(shadows: [
                Shadow(
                  blurRadius: 5.0,
                  color: Colors.black.withValues(alpha: 0.38),
                  offset: const Offset(0.0, -1),
                ),
              ], fontSize: 16, color: AppColor.primaryText, fontWeight: FontWeight.w500),
            ),
          ),
        ),
      ],
    );
  }
/*
  //宝石
  List<Widget> _actions() {
    return [
      InkWell(
        onTap: () {
          controller.fineTuneLogic.showCompleted();
        },
        child: Container(
          height: 26.w,
          margin: EdgeInsets.only(right: 16.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(26.w / 2),
            color: AppColor.colorsUtil('#D9BD90'),
            border: Border.all(
              color: AppColor.colorsUtil('#A9936F'),
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          child: Row(
            children: [
              Image.asset(
                Assets.assetsImagesSessionGems,
                fit: BoxFit.contain,
                width: 20.w,
              ),
              3.horizontalSpace,
              Center(
                child: Text(
                  CommonUtil.numberUnits(10000),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColor.primaryText,
                    height: 1.w,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      )
    ];
  }
*/
}
