import 'dart:io';

import 'package:amor_app/common/models/ai_models/ai_styles_model.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/service/user_service.dart';
import 'package:amor_app/common/utils/common_util/common_util.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_input.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_styles.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiStep2 extends StatefulWidget {
  const AiStep2({
    super.key,
    required this.onTapGen,
    this.onDeleteImage,
    this.role,
    required this.isVideo,
    required this.onInputTextFinish,
    required this.styles,
    required this.onChooseStyles,
    this.imagePath,
    required this.undressRole,
    this.selectedStyel,
  });

  final VoidCallback onTapGen;
  final VoidCallback? onDeleteImage;
  final AmorsFeedModel? role;
  final bool isVideo;
  final Function(String text) onInputTextFinish;
  final List<AiStylesModel> styles;
  final Function(AiStylesModel? style) onChooseStyles;
  final String? imagePath;
  final bool undressRole;
  final AiStylesModel? selectedStyel;

  @override
  State<AiStep2> createState() => _AiStep2State();
}

class _AiStep2State extends State<AiStep2> {
  AiStylesModel? style;
  String? customPrompt;

  @override
  void initState() {
    style = widget.selectedStyel;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final imgW = MediaQuery.sizeOf(context).width - 24;
    final imgH = imgW / 3 * 4;

    bool hasCustomPrompt = customPrompt != null && customPrompt!.isNotEmpty;
    var avatar = widget.role?.avatar;

    var imagePath = widget.imagePath;

    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            margin: EdgeInsets.only(top: 8),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              clipBehavior: Clip.hardEdge, // 确保启用裁剪
              child: Container(
                color: Color(0x1AFFFFFF),
                height: imgH,
                width: imgW,
                child: Stack(
                  alignment: Alignment.topRight,
                  children: [
                    if (widget.undressRole && avatar != null)
                      Positioned.fill(
                        child: CachedNetworkImage(
                          imageUrl: avatar,
                          fit: BoxFit.cover,
                        ),
                      ),
                    if (imagePath != null && imagePath.isNotEmpty)
                      Positioned.fill(
                        child: Image.file(File(imagePath), fit: BoxFit.cover),
                      ),
                    IconButton(
                      onPressed: widget.onDeleteImage,
                      icon: const Icon(Icons.cancel, color: Colors.black, size: 32),
                    )
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          if (widget.isVideo == false) ...[
            AiStyles(
              selectedStyel: style,
              list: widget.styles,
              onChooseed: onChooseedStyle,
            ),
            SizedBox(height: 8),
          ],
          Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'custom_prompt'.tr,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(height: 4),
          InkWell(
            onTap: onTapInput,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              margin: EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Color(0x1AFFFFFF),
                borderRadius: BorderRadius.circular(8),
              ),
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                hasCustomPrompt
                    ? customPrompt!
                    : widget.isVideo
                        ? 'prompt_examples_video'.tr
                        : 'prompt_examples_img'.tr,
                style: TextStyle(
                  color: hasCustomPrompt ? Colors.white : Color(0x66FFFFFF),
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Obx(() {
            final user = UserService.to.userinfo.value;
            var createImg = user.createImg ?? 0;
            var createVideo = user.createVideo ?? 0;
            return Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: 'balance'.tr,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  WidgetSpan(
                    child: SizedBox(width: 2),
                  ),
                  TextSpan(
                    text: widget.isVideo ? '$createVideo' : '$createImg',
                    style: TextStyle(
                      color: Color(0xFFFFDCA4),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  WidgetSpan(
                    child: SizedBox(width: 2),
                  ),
                  TextSpan(
                    text: widget.isVideo ? 'videos'.tr : 'photos'.tr,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(height: 8),
          InkWell(
            onTap: widget.onTapGen,
            child: Container(
              height: 44,
              width: 240,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(22),
                color: CommonUtil.colorsUtil('#4E4E4E'),
                gradient: LinearGradient(
                  colors: [
                    CommonUtil.colorsUtil('#C69351'),
                    CommonUtil.colorsUtil('#EAC282'),
                  ],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                ),
              ),
              child: Center(
                child: Text(
                  'Generate'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  void onTapInput() {
    Get.bottomSheet(
      AiInput(
        customPrompt: customPrompt,
        isVideo: widget.isVideo,
        onInputTextFinish: (v) {
          customPrompt = v;
          style = null;
          widget.onChooseStyles(null);
          widget.onInputTextFinish(v);
          setState(() {});
        },
      ),
      enableDrag: false,
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  void onChooseedStyle(AiStylesModel data) {
    style = data;
    customPrompt = null;
    widget.onChooseStyles(data);
    widget.onInputTextFinish('');
    setState(() {});
  }
}
