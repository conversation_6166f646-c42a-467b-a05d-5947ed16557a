import 'dart:convert';

import 'package:amor_app/common/apis/profile_apis/profile_apis.dart';
import 'package:amor_app/common/service/app_service.dart';
import 'package:amor_app/common/service/sp_service.dart';
import 'package:amor_app/common/service/user_service.dart';
import 'package:amor_app/common/utils/analytics_util/analytics.dart';
import 'package:amor_app/common/values/sp_service_key.dart';
import 'package:amor_app/common/widgets/loading/loading.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiSkuCtr extends GetxController {
  static AiSkuCtr get to => Get.find();

  bool isVideo = false;

  @override
  void onInit() {
    super.onInit();
    isVideo = Get.arguments['isVideo'] as bool;
  }

  verifyInAppPurchase() async {
    String productId = '';
    List list = (AppService.sp.get(spInAppPurchaseOrder) as List?) ?? [];
    if (list.isEmpty) {
      return;
    }
    List verifyList = List.from(list);
    int buyType = 2;
    Loading.show();
    for (int i = 0; i < list.length; i++) {
      Map<String, dynamic>? params = jsonDecode(list.elementAt(i));
      if (params == null || params['serviceParam'] == null) {
        continue;
      }

      Map<String, dynamic> serviceParam = params['serviceParam'];
      serviceParam['source'] = PurchaseSheet.modelIdSource;
      if (isVideo) {
        serviceParam['createVideo'] = true;
      } else {
        serviceParam['createImg'] = true;
      }

      Map? result = await ProfileApis.inAppPurchaseVerify(params: serviceParam);
      if (result != null) {
        buyType = serviceParam['type'];
        productId = params['serviceParam']['goodsNo'] ?? '';
        try {
          verifyList.replaceRange(i, i + 1, ['verify']);
        } catch (e) {
          debugPrint('Error');
        }
      }
    }
    verifyList.removeWhere((element) {
      return element == 'verify';
    });
    Get.find<SPService>().set(spInAppPurchaseOrder, verifyList);

    await UserService.to.getUserInfo();
    Loading.dismiss();
    //宝石购买
    Future.delayed(const Duration(milliseconds: 500), () {
      if (verifyList.isEmpty) {
        Get.back();
      }
    });
    var from = isVideo ? 'img2v' : 'aiphoto';
    Analytics().paySuccessEvent(source: 'suc_gems_${productId}_$from', type: 1, productId: productId);
  }
}
