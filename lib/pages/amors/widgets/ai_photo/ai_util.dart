import 'dart:convert';
import 'dart:io';

import 'package:amor_app/common/utils/analytics_util/analytics.dart';
import 'package:amor_app/common/utils/media_util/media_util.dart';
import 'package:amor_app/pages/session/sub_pages/session_set_bg/widgets/permissions_dialog.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class ObfuscatedAssets {
  static const _imageEncoded = 'aHR0cHM6Ly9zdGF0aWMuYW1vcmFpLm5ldC8yMDI1LTA2LTExLzIwMjUwNjExLTE4MDk1Ni5qcGVn';
  static const _videoEncoded = 'aHR0cHM6Ly9zdGF0aWMuYW1vcmFpLm5ldC9pbWFnZXMvMTkyNjg4Nzk0NTA3ODM1ODAxOC5tcDQ=';

  static String _decode(String base64Str) => utf8.decode(base64.decode(base64Str));

  static String get imageUrl => _decode(_imageEncoded);
  static String get videoUrl => _decode(_videoEncoded);
}

/// 对文件进行 md5 计算
Future<String> calculateMd5(File file) async {
  try {
    final bytes = await file.readAsBytes();
    return md5.convert(bytes).toString();
  } catch (e) {
    debugPrint('calculateMd5 ❌: $e');
    return '';
  }
}

Future<File?> processImage(File file) async {
  // 压缩并转换为 JPG 格式
  final compressedFile = await compressAndConvertToJpg(file);
  return compressedFile;
}

Future<File?> compressAndConvertToJpg(File file, {int initialQuality = 85, int minSize = 2 * 1024 * 1024}) async {
  try {
    int fileSize = await file.length();
    if (fileSize <= minSize && file.path.split('.').last.toLowerCase() == 'jpg') {
      return file;
    }

    int quality = initialQuality;
    File? compressedFile;

    do {
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      // 创建新的文件路径
      final targetPath = join(tempDir.path, '${DateTime.now().millisecondsSinceEpoch}.jpg');

      final XFile? compressedXFile = await FlutterImageCompress.compressAndGetFile(
        file.absolute.path,
        targetPath, // 将压缩后的图片保存到不同路径
        quality: quality,
        format: CompressFormat.jpeg,
      );

      if (compressedXFile != null) {
        compressedFile = File(compressedXFile.path);
        int compressedFileSize = await compressedFile.length();

        if (compressedFileSize <= minSize) {
          return compressedFile;
        } else {
          quality -= 5; // 每次减少5的质量
          if (quality < 10) break; // 避免质量过低
        }
      } else {
        return null;
      }
    } while (quality >= 10);

    return compressedFile;
  } catch (e) {
    debugPrint('compressAndConvertToJpg ❌: $e');
  }
  return null;
}

class AiUtil {
  static Future<String?> selectImage() async {
    Analytics().logEvent(Analytics.cunupload);
    String? path = await SelectMediaUtil.selectImage(crop: false);
    return path;
  }

  //缺少相册权限
  noPermissions() async {
    int? result = await Get.dialog(const PermissionsDialogWidget());
    if (result == 1) {
      openAppSettings();
    }
  }
}
