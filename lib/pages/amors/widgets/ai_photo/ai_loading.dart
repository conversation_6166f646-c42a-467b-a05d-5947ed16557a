import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class AiLoading extends StatelessWidget {
  const AiLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xB3060818),
      padding: EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 48,
            height: 48,
            child: Lottie.asset('assets/lottie/clothing_load.json', animate: true),
          ),
          SizedBox(height: 14),
          Text(
            'ai_generating'.tr,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 24),
          Text(
            'generating_masterpiece'.tr,
            style: TextStyle(
              color: Colors.white,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
          Sized<PERSON><PERSON>(height: 8),
          Text(
            'art_consumes_power'.tr,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.6),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
