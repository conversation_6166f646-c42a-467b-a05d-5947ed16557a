import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/widgets/custom_widget/custom_widget.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiRolePage extends StatefulWidget {
  const AiRolePage({super.key});

  @override
  State<AiRolePage> createState() => _AiRolePageState();
}

class _AiRolePageState extends State<AiRolePage> {
  int? modelId;
  String? avatar;
  @override
  void initState() {
    super.initState();

    var params = Get.arguments as Map<String, dynamic>?;
    modelId = params?['modelId'];
    avatar = params?['avatar'];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: const CustomBackButton(),
        title: Text('Undress'.tr),
      ),
      body: AiView(
        key: <PERSON><PERSON><PERSON>('page'),
        isVideo: false,
        role: AmorsFeedModel(modelId: modelId, avatar: avatar),
      ),
    );
  }
}
