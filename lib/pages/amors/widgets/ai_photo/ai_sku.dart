import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/app_default_value.dart';
import 'package:amor_app/common/values/assets.dart';
import 'package:amor_app/common/widgets/custom_widget/custom_widget.dart';
import 'package:amor_app/common/widgets/loading/loading.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_sku_ctr.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiSku extends StatefulWidget {
  const AiSku({super.key});

  @override
  State<AiSku> createState() => _AiSkuState();
}

class _AiSkuState extends State<AiSku> {
  List<AppPurchaseModel> storeGemsConfigList = [];
  bool isVideo = false;
  AppPurchaseModel? _chooseProduct;

  @override
  void initState() {
    super.initState();

    isVideo = Get.arguments['isVideo'] as bool;

    Get.put(AiSkuCtr());

    loadSku();
  }

  void loadSku() async {
    bool isAvailable = await InAppPurchaseUtil().initStoreInfo();
    if (isAvailable == false) {
      Loading.toast('Temporarily unable to make a purchase');
      return;
    }
    if (CacheSaleService.storeSsvipConfigList.isNotEmpty) {
      storeGemsConfigList = CacheSaleService.storeGemsConfigList;
    }
    _chooseProduct = storeGemsConfigList.firstWhereOrNull((e) => e.serviceProductDetails?.selected == true);
    setState(() {});
  }

  void buy() async {
    var productDetails = _chooseProduct?.productDetails;

    if (productDetails == null) {
      Loading.toast('please select sku');
      return;
    }
    InAppPurchaseUtil().fetchPurchase(productDetails: productDetails);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: Container(),
        title: Text('purchase_balance'.tr),
        actions: [
          CustomBackButton(
            img: Assets.assetsImagesNavCloseBlackCircle,
            width: 28,
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(child: _buildList()),
          InkWell(
            onTap: buy,
            child: Container(
              height: 44,
              width: 240,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(22),
                color: CommonUtil.colorsUtil('#4E4E4E'),
                gradient: LinearGradient(
                  colors: [
                    CommonUtil.colorsUtil('#C69351'),
                    CommonUtil.colorsUtil('#EAC282'),
                  ],
                  begin: Alignment.bottomLeft,
                  end: Alignment.topRight,
                ),
              ),
              child: Center(
                child: Text(
                  'CONTINUE'.tr,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ListView _buildList() {
    return ListView.separated(
      padding: EdgeInsets.all(12),
      separatorBuilder: (context, index) => SizedBox(height: 20),
      itemBuilder: (_, index) {
        final model = storeGemsConfigList[index];
        final serviceProductDetails = model.serviceProductDetails;
        final productDetails = model.productDetails;

        final bestChoice = serviceProductDetails?.tag == '1';
        final most = isVideo ? false : serviceProductDetails?.tag == '2';

        final isSelected = _chooseProduct?.serviceProductDetails?.goodsNo == serviceProductDetails?.goodsNo;

        String price = productDetails?.price ?? '';
        var uniPart = isVideo ? 'videos'.tr : 'photos'.tr;

        var rawPrice = model.productDetails?.rawPrice ?? 0;
        var count = (isVideo ? serviceProductDetails?.createVideo : serviceProductDetails?.createImg) ?? 1;
        var title = '$count $uniPart';

        var oneRawPrice = rawPrice / count;
        double truncated = (oneRawPrice * 100).truncateToDouble() / 100;
        String formattedNumber = truncated.toStringAsFixed(2);
        var onePrice = '${productDetails?.currencySymbol}$formattedNumber/$uniPart';

        return GestureDetector(
          onTap: () {
            _chooseProduct = model;
            setState(() {});
            buy();
          },
          child: Stack(
            alignment: AlignmentDirectional.topEnd,
            children: [
              Container(
                padding: EdgeInsets.all(20),
                margin: EdgeInsets.only(top: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      Color(0x807B6000),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  border: Border.all(width: 2, color: isSelected ? Color(0xFFE8C788) : Color(0x26FFFFFF)),
                ),
                child: Row(
                  spacing: 8,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Column(
                        spacing: 4,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            price,
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            onePrice,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Colors.white.withValues(alpha: 0.7),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Text(
                        title,
                        textAlign: TextAlign.end,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                          color: Color(0xFFF0BE72),
                          fontStyle: FontStyle.italic,
                          fontFamily: fontBeVietnamPro,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (most == true) _buildTag('most_popular'.tr),
              if (bestChoice == true) _buildTag('best_value'.tr)
            ],
          ),
        );
      },
      itemCount: storeGemsConfigList.length,
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xFFFBA000),
            Color(0xFFFC680B),
          ],
        ),
        borderRadius: BorderRadiusDirectional.only(
          topEnd: Radius.circular(16),
          bottomStart: Radius.circular(16),
        ),
      ),
      child: Text(
        tag,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: Colors.white.withValues(alpha: 0.7),
          fontStyle: FontStyle.italic,
        ),
      ),
    );
  }
}
