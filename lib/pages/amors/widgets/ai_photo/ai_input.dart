import 'package:amor_app/common/values/assets.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiInput extends StatefulWidget {
  const AiInput({
    super.key,
    this.customPrompt,
    required this.onInputTextFinish,
    required this.isVideo,
  });

  final String? customPrompt;
  final Function(String text) onInputTextFinish;
  final bool isVideo;

  @override
  State<AiInput> createState() => _AiInputState();
}

class _AiInputState extends State<AiInput> {
  final focusNode = FocusNode();
  final textController = TextEditingController();

  @override
  void initState() {
    focusNode.requestFocus();
    textController.addListener(_onTextChanged);
    if (widget.customPrompt != null && widget.customPrompt!.isNotEmpty) {
      textController.text = widget.customPrompt!;
    }
    super.initState();
  }

  void _onTextChanged() {
    if (textController.text.length > 500) {
      Loading.toast('max_input_length'.tr);

      // 截断文本到500字符
      textController.text = textController.text.substring(0, 500);
      // 将光标移到文本末尾
      textController.selection = TextSelection.fromPosition(
        TextPosition(offset: textController.text.length),
      );
    }
  }

  void _onSure() {
    focusNode.unfocus();
    // 将值回调出去
    widget.onInputTextFinish(textController.text);
    Get.back();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        focusNode.unfocus();
      },
      child: Container(
        margin: EdgeInsets.only(top: 120),
        decoration: BoxDecoration(
          color: Color(0xFF333333),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          border: Border.all(
            color: Color(0xCC666666),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CustomBackButton(
                  img: Assets.assetsImagesNavCloseBlackCircle,
                  width: 24,
                  back: () {
                    focusNode.unfocus();
                    Get.back();
                  },
                ),
                Spacer(),
                IconButton(
                  padding: EdgeInsets.all(16),
                  onPressed: _onSure,
                  icon: Image.asset(
                    Assets.assetsImagesGemsShortSelected,
                    width: 24,
                  ),
                ),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'custom_prompt'.tr,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            SizedBox(height: 8),
            Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16),
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                decoration: BoxDecoration(
                  color: Color(0x1AFFFFFF),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  autofocus: true,
                  textInputAction: TextInputAction.newline, // 修改为换行操作
                  maxLines: null, // 允许多行输入
                  minLines: 5, // 最小显示5行
                  enableInteractiveSelection: true, // 确保文本选择功能启用
                  dragStartBehavior: DragStartBehavior.down, // 优化拖拽行为
                  maxLength: null,
                  style: const TextStyle(
                    height: 1.5, // 增加行高
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  controller: textController,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.zero, // 添加内边距
                    hintText: widget.isVideo ? 'prompt_examples_video'.tr : 'prompt_examples_img'.tr,
                    counterText: '',
                    hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.6)),
                    fillColor: Colors.transparent,
                    border: InputBorder.none,
                    filled: true,
                    isDense: true,
                  ),
                  focusNode: focusNode,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
