import 'package:amor_app/common/models/ai_models/ai_styles_model.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get_utils/src/extensions/internacionalization.dart';

class AiStyles extends StatelessWidget {
  const AiStyles({
    super.key,
    required this.list,
    required this.onChooseed,
    this.selectedStyel,
  });

  final List<AiStylesModel> list;
  final AiStylesModel? selectedStyel;
  final void Function(AiStylesModel data) onChooseed;

  @override
  Widget build(BuildContext context) {
    if (list.isEmpty) {
      return const SizedBox();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        spacing: 4,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'styles'.tr,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.all(0),
            physics: const NeverScrollableScrollPhysics(),
            itemCount: list.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              childAspectRatio: 79.0 / 32.0,
            ),
            itemBuilder: (_, index) {
              final item = list[index];
              final isSelected = item.style == selectedStyel?.style;
              return _buildItem(item, isSelected);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildItem(AiStylesModel item, bool isSelected) {
    return GestureDetector(
      onTap: () {
        onChooseed(item);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: Color(0x1AFFFFFF),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: isSelected ? Color(0xFFFFDCA4) : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          spacing: 4,
          children: [
            CachedNetworkImage(
              imageUrl: item.icon ?? '',
              width: 14,
              color: Color(0xFFFFDCA4),
            ),
            Expanded(
              child: Text(
                item.name ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: isSelected ? Color(0xFFFFDCA4) : Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
