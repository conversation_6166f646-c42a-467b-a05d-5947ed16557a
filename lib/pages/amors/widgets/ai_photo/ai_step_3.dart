import 'dart:io';

import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/utils/common_util/common_util.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/preview_image_page.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/preview_video_page.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

class AiStep3 extends StatefulWidget {
  const AiStep3({
    super.key,
    this.role,
    required this.onTapGen,
    this.onDeleteImage,
    required this.resultUrl,
    required this.isVideo,
  });

  final AmorsFeedModel? role;
  final VoidCallback onTapGen;
  final VoidCallback? onDeleteImage;
  final String resultUrl;
  final bool isVideo;

  @override
  State<AiStep3> createState() => _AiStep3State();
}

class _AiStep3State extends State<AiStep3> {
  VideoPlayerController? _controller;

  @override
  void initState() {
    super.initState();
    if (widget.isVideo) {
      _controller = VideoPlayerController.file(
        File(widget.resultUrl),
      )..initialize().then((_) {
          _controller?.setLooping(true);
          _controller?.play();
          setState(() {});
        });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AiStep3 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVideo && widget.resultUrl != oldWidget.resultUrl) {
      _controller?.dispose();

      _controller = VideoPlayerController.file(
        File(widget.resultUrl),
      )..initialize().then((_) {
          _controller?.setLooping(true);
          _controller?.play();
          setState(() {});
        });
    }
  }

  @override
  Widget build(BuildContext context) {
    bool hasRole = widget.role != null;

    final imgW = MediaQuery.sizeOf(context).width - 24;
    final imgH = imgW / 3 * 4;

    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12),
          margin: EdgeInsets.only(top: 8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            clipBehavior: Clip.hardEdge, // 确保启用裁剪
            child: Container(
              color: Color(0x1AFFFFFF),
              height: imgH,
              width: imgW,
              child: Stack(
                alignment: Alignment.topRight,
                children: [
                  Positioned.fill(
                    child: InkWell(
                      child: widget.isVideo
                          ? (_controller?.value.isInitialized ?? false)
                              ? AspectRatio(
                                  aspectRatio: _controller!.value.aspectRatio,
                                  child: VideoPlayer(_controller!),
                                )
                              : Center(
                                  child: SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: const CircularProgressIndicator(
                                      strokeWidth: 2,
                                      backgroundColor: Colors.white,
                                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFDCA4)),
                                    ),
                                  ),
                                )
                          : CachedNetworkImage(
                              imageUrl: widget.resultUrl,
                              fit: BoxFit.cover,
                            ),
                      onTap: () {
                        if (widget.isVideo) {
                          Get.to(PreviewVideoPage(), arguments: widget.resultUrl);
                        } else {
                          Get.to(PreviewImagePage(), arguments: widget.resultUrl);
                        }
                      },
                    ),
                  ),
                  IconButton(
                    onPressed: widget.onDeleteImage,
                    icon: const Icon(Icons.cancel, color: Colors.black, size: 32),
                  )
                ],
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 8,
          left: 0,
          right: 0,
          child: Center(
            child: InkWell(
              onTap: widget.onTapGen,
              child: Container(
                height: 44,
                width: 240,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(22),
                  color: CommonUtil.colorsUtil('#4E4E4E'),
                  gradient: LinearGradient(
                    colors: [
                      CommonUtil.colorsUtil('#C69351'),
                      CommonUtil.colorsUtil('#EAC282'),
                    ],
                    begin: Alignment.bottomLeft,
                    end: Alignment.topRight,
                  ),
                ),
                child: Center(
                  child: Text(
                    'generate_another_one'.tr,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: 12)
      ],
    );
  }
}
