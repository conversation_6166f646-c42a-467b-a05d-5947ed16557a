import 'package:amor_app/common/apis/ai_apis/ai_apis.dart';
import 'package:amor_app/common/apis/session_apis/session_apis.dart';
import 'package:amor_app/common/models/ai_models/ai_histroy_model.dart';
import 'package:amor_app/common/models/ai_models/ai_styles_model.dart';
import 'package:amor_app/common/models/ai_models/ai_upload_model.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/routes/pages.dart';
import 'package:amor_app/common/service/user_service.dart';
import 'package:amor_app/common/utils/analytics_util/analytics.dart';
import 'package:amor_app/common/utils/http_util/api_request.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_loading.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_step_1.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_step_2.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_step_3.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AiView extends StatefulWidget {
  const AiView({
    super.key,
    required this.isVideo,
    this.role,
  });

  final bool isVideo;
  final AmorsFeedModel? role;

  @override
  State<AiView> createState() => _AiViewState();
}

enum AiStep {
  step1,
  step2,
  step3,
}

class _AiViewState extends State<AiView> {
  AiStep step = AiStep.step1;
  String imagePath = '';
  String? customPrompt;

  bool isLoading = false;
  bool undressRole = false;

  List<AiStylesModel> styles = [];
  AiStylesModel? selectedStyel;

  List<AiHistroyModel>? records;
  bool hasHistory = false;

  AmorsFeedModel? role;

  String? imageUrl;

  @override
  void initState() {
    super.initState();
    role = widget.role;

    loadStyles();
    if (widget.role != null) {
      loadHistory();
    }
    UserService.to.getUserInfo();
  }

  Future loadStyles() async {
    if (styles.isNotEmpty) {
      return;
    }
    try {
      List<AiStylesModel> list = await AiApis.fetchStyleConf();
      if (list.isNotEmpty) {
        styles = list;
      }
      setState(() {});
    } catch (e) {
      debugPrint('getStyleConfig ❌: $e');
    }
  }

  // 获取历史记录
  void loadHistory() async {
    var characterId = role?.modelId;
    if (characterId == null) {
      return;
    }
    Loading.show();
    final list = await AiApis.getHistory(characterId);
    records = list;
    hasHistory = records != null && records!.isNotEmpty;
    setState(() {});
    Loading.dismiss();
  }

  void onTapUpload() async {
    var path = await AiUtil.selectImage();
    if (path == null || path.isEmpty) return;
    imagePath = path;

    if (styles.isEmpty) {
      await loadStyles();
    }
    step = AiStep.step2;
    undressRole = false;
    selectedStyel = styles.firstOrNull;
    setState(() {});
  }

  void onTapGenRole() async {
    await loadStyles();
    if (hasHistory) {
      var res = records?.firstOrNull;
      imageUrl = res?.url;
      step = AiStep.step3;
      setState(() {});
    } else {
      final roleId = role?.modelId;
      if (roleId == null) return;
      undressRole = true;
      step = AiStep.step2;
      selectedStyel = styles.firstOrNull;
      setState(() {});
    }
  }

  void onTapGen() async {
    Analytics().logEvent(Analytics.c_un_generate);

    Loading.show();
    await UserService.to.getUserInfo();
    Loading.dismiss();

    if (widget.isVideo) {
      genVideo();
    } else {
      genImage();
    }
  }

  void stopLoading({bool showToast = false}) {
    isLoading = false;
    setState(() {});
    if (showToast) {
      Loading.toast('generation_failed_message'.tr);
    }
  }

  void genSucc() {
    UserService.to.getUserInfo();

    step = AiStep.step3;
    Analytics().logEvent(Analytics.un_gen_suc);
    stopLoading();
  }

  void buySku() async {
    stopLoading();
    Get.toNamed(Routes.aiSku, arguments: {'isVideo': widget.isVideo});
  }

  void genImage() {
    var undressCount = UserService.to.userinfo.value.createImg ?? 0;
    if (undressCount <= 0) {
      buySku();
      return;
    }

    if (undressRole) {
      undrRole();
    } else {
      undreImg();
    }
  }

  Future<String> getStyle() async {
    var style = '';
    if (customPrompt != null && customPrompt!.isNotEmpty) {
      String? enText = await SessionApis.sessionMessageTranslate(
        tarLang: (Get.deviceLocale ?? const Locale('en', 'US')).toString(),
        souLang: 'en_US',
        text: customPrompt ?? '',
      );
      style = enText ?? '';
    } else {
      style = selectedStyel?.style ?? '';
    }
    return style;
  }

  Future undrRole() async {
    try {
      var characterId = widget.role?.modelId;
      if (characterId == null) return;

      setState(() {
        isLoading = true;
      });

      var style = await getStyle();
      final data = await AiApis.uploadRoleImage(style: style, characterId: characterId);

      final img = data?.uid;

      if (img != null && img.contains('http')) {
        imageUrl = img;
        UserService.to.getUserInfo();

        await Future.delayed(const Duration(seconds: 10));
        genSucc();
        loadHistory();
        return;
      }

      final taskId = data?.uid;
      if (taskId == null) {
        stopLoading();
        return;
      }

      // 预估时间
      final estimateTime = data?.estimatedTime ?? 0;
      await Future.delayed(Duration(seconds: estimateTime));

      final result = await AiApis.getImageResult(taskId);
      var status = result?.status ?? 0;
      if (status != 2) {
        stopLoading(showToast: true);
        return;
      }

      imageUrl = result?.results?.first;

      if (imageUrl == null) {
        stopLoading(showToast: true);
        return;
      }
      genSucc();

      loadHistory();
    } catch (e) {
      stopLoading();
    }
  }

  Future undreImg() async {
    try {
      setState(() {
        isLoading = true;
      });

      // 上传图片
      final uploadRes = await AiApis.uploadImage(imagePath: imagePath, style: await getStyle());
      if (uploadRes == null) {
        stopLoading(showToast: true);
        return;
      }

      final taskId = uploadRes.uid;
      if (taskId == null) {
        stopLoading(showToast: true);
        return;
      }

      if (taskId.contains('http')) {
        imageUrl = taskId;
        await Future.delayed(const Duration(seconds: 10));
        genSucc();
        return;
      }
      // 预估时间
      final estimateTime = uploadRes.estimatedTime ?? 0 + 10;
      await Future.delayed(Duration(seconds: estimateTime));

      final res = await AiApis.getImageResult(taskId);
      imageUrl = res?.results?.first;

      if (imageUrl == null) {
        stopLoading(showToast: true);
        return;
      }
      genSucc();
    } catch (e) {
      stopLoading(showToast: true);
    }
  }

  void genVideo() async {
    var undressCount = UserService.to.userinfo.value.createVideo ?? 0;
    if (undressCount <= 0) {
      buySku();
      return;
    }
    if (customPrompt == null || customPrompt!.isEmpty) {
      Loading.toast('please_enter_custom_prompt'.tr);
      stopLoading();
      return;
    }

    setState(() {
      isLoading = true;
    });

    // 翻译 customPrompt：
    String? enText = await SessionApis.sessionMessageTranslate(
      souLang: (Get.deviceLocale ?? const Locale('en', 'US')).toString(),
      tarLang: 'en_US',
      text: customPrompt ?? '',
    );

    if (enText == null) {
      Loading.toast('please_enter_custom_prompt'.tr);
      setState(() {
        isLoading = false;
      });
      return;
    }

    // 上传图片，开始任务
    var uploadRes = await AiApis.uploadImgToVideo(imagePath: imagePath, enText: enText);
    // 获取结果
    if (uploadRes == null) {
      stopLoading();
      return;
    }

    final taskId = uploadRes.uid;
    if (taskId == null) {
      stopLoading();
      return;
    }

    if (taskId.contains('http')) {
      imageUrl = taskId;
      await Future.delayed(const Duration(seconds: 10));
      genSucc();
      return;
    }

    // 预估时间
    final estimateTime = uploadRes.estimatedTime ?? 0;
    await Future.delayed(Duration(seconds: estimateTime));

    AiVideoResItem? res = await AiApis.getVideoResult(taskId);
    var videoUrl = res?.resultPath;
    if (videoUrl == null) {
      stopLoading(showToast: true);
      return;
    }
    imageUrl = await ApiRequest.downloadVideo(videoUrl);

    genSucc();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          child: _body(),
        ),
        if (isLoading) AiLoading(),
      ],
    );
  }

  Widget _body() {
    if (step == AiStep.step1) {
      return AiStep1(
        role: widget.role,
        isVideo: widget.isVideo,
        hasHistory: hasHistory,
        onTapGenRole: onTapGenRole,
        onTapUpload: onTapUpload,
      );
    }
    if (step == AiStep.step2) {
      return AiStep2(
        onTapGen: onTapGen,
        onDeleteImage: () {
          imagePath = '';
          step = AiStep.step1;
          setState(() {});
        },
        role: widget.role,
        isVideo: widget.isVideo,
        onInputTextFinish: (String text) {
          customPrompt = text;
        },
        styles: styles,
        onChooseStyles: (value) {
          selectedStyel = value;
        },
        imagePath: imagePath,
        undressRole: undressRole,
        selectedStyel: selectedStyel,
      );
    }

    if (step == AiStep.step3) {
      return AiStep3(
        onTapGen: onTapUpload,
        onDeleteImage: () {
          imagePath = '';
          step = AiStep.step1;
          setState(() {});
        },
        role: widget.role,
        resultUrl: imageUrl ?? '',
        isVideo: widget.isVideo,
      );
    }

    return Container();
  }
}
