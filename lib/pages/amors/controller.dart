import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/amors_models/amors_category_model.dart';
import 'package:amor_app/common/models/amors_models/amors_feed_model.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/tags/tags_select_widget.dart';
import 'package:amor_app/pages/amors/widgets/search.dart';
import 'package:amor_app/pages/inner/controller.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:amor_app/common/routes/routes.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:get/get.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:share_plus/share_plus.dart';
import 'logic/search_controller.dart';
import 'state.dart';

class AmorsPageController extends GetxController with GetSingleTickerProviderStateMixin {
  static AmorsPageController get to => Get.find();
  final state = AmorsPageControllerState();
  late EasyRefreshController discoverRefreshCtl;
  late ItemScrollController tagScrollCtl;
  late ScrollController scrollCtl;
  @override
  void onInit() {
    super.onInit();
    discoverRefreshCtl = EasyRefreshController(controlFinishRefresh: true, controlFinishLoad: true);
    tagScrollCtl = ItemScrollController();
    state.showSearch.value = !AppService.audit;
    scrollCtl = ScrollController();
    getCategorys();
  }

  //获取 标签 列表
  getCategorys() async {
    AmorsApis.getCategorys(
      cacheCallBack: (cache) {
        if (cache.isNotEmpty) {
          if (state.tagsList.length > 1 && AppService.audit == false) {
            state.tagsList.value = cache;
            selectedCategory(0);
          }
        }
      },
    ).then((value) {
      if (state.tagsList.isEmpty && value.isNotEmpty) {
        state.tagsList.value = value;
        selectedCategory(0);
      }
    });
  }

  //选择分类
  selectedCategory(int index, {bool loadmore = false, bool refresh = false}) {
    if (state.tagsList.length <= index) {
      return;
    }
    try {
      tagScrollCtl.scrollTo(
        index: index,
        duration: const Duration(milliseconds: 200),
        alignment: 0.4,
      );
    } catch (e) {}
    AmorsCategoryModel model = state.tagsList.elementAt(index);
    ReportUtil.reportEvents(
        page: ReportUtil.amor, action: ReportUtil.select, value: model.cateName);
    if (!loadmore) {
      state.discoverPage = 1;
    }
    getDataList(cateId: model.id.toString(), page: state.discoverPage, refresh: refresh);
  }

  //获取分类列表
  getDataList({required String cateId, required int page, bool refresh = false}) async {
    List<AmorsFeedModel> list = [];
    AmorsApis.getCategoryDataList(
      cacheCallBack: (cache) {
        if (refresh == false && cache.isNotEmpty && AppService.audit == false) {
          list = cache;
          setDataList(list: cache, cache: true, cateId: cateId);
        }
      },
      cateId: cateId,
      page: page,
      modelId: Get.find<SPService>().get(spExposureModel) == null
          ? null
          : (Get.find<SPService>().get(spExposureModel) as int),
    ).then((value) {
      //没有缓存数据
      if (list.isEmpty) {
        setDataList(list: value, cache: false, cateId: cateId);
      }
    });
  }

  setDataList({required List<AmorsFeedModel> list, required bool cache, required String cateId}) {
    discoverRefreshCtl.finishRefresh();
    //请求到数据时已经切换分类
    if ((state.tagsList.elementAt(state.selectedCategoryIndex.value) as AmorsCategoryModel)
            .id
            .toString() !=
        cateId) {
      discoverRefreshCtl.finishLoad(IndicatorResult.success);
      discoverRefreshCtl.finishRefresh();
      return;
    }
    //自动请求下一个分类时 不使用缓存
    if (cache == true && state.clearList == false) {
      return;
    }
    if (state.discoverPage == 1 && state.clearList == true) {
      state.dataList.assignAll(list);
    } else {
      state.dataList.addAllIf(list.isNotEmpty, list);
    }

    if (list.isEmpty) {
      //最后一个分类返回空 展示 no more
      if (state.selectedCategoryIndex.value == state.tagsList.length - 1) {
        discoverRefreshCtl.finishLoad(IndicatorResult.noMore);
      } else if (cache == false) {
        // 请求下一个分类
        state.selectedCategoryIndex.value++;
        state.clearList = false;
        selectedCategory(state.selectedCategoryIndex.value);
      }
    } else {
      if (state.discoverPage == 1) {
        discoverRefreshCtl.resetFooter();
        if (state.clearList == false) {
          discoverRefreshCtl.finishLoad(IndicatorResult.success);
        }
      } else {
        discoverRefreshCtl.finishLoad(IndicatorResult.success);
      }
    }

    if (cateId == '-2' &&
        state.discoverPage == 1 &&
        state.dataList.length > 7 &&
        UserService.to.isLogin == true) {
      Get.find<SPService>()
          .set(spExposureModel, (state.dataList.elementAt(7) as AmorsFeedModel).modelId ?? 0);
    }
  }

  //like
  likeOperation({int? index, required int modelId}) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    //震动
    if ((Get.find<SPService>().get(spHipticsValue) ?? true) == true && index != null) {
      HapticFeedback.mediumImpact();
    }
    AmorsFeedModel? model;
    //其他页面操作刷新
    if (index == null) {
      model =
          state.dataList.firstWhereOrNull((value) => (value as AmorsFeedModel).modelId == modelId);
    } else {
      model = state.dataList.elementAt(index);
    }
    if (model?.modelId == null) {
      return;
    }
    if (model?.hasLike == true) {
      if (index != null) {
        Map? shareParam = await CommonApis.commonShare(modelId: model?.modelId);
        if (shareParam != null) {
          Share.share(shareParam['text']);
          ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.share);
        }
      } else {
        index = state.dataList.indexOf(model);
        model?.hasLike = false;
        state.dataList.replaceRange(index, index + 1, [model]);
      }
    } else {
      if (index != null) {
        model?.hasLike = true;
        state.dataList.replaceRange(index, index + 1, [model]);
        ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.like);
        bool success = await AmorsApis.doLike(modelId: model?.modelId);
        if (success && Get.isRegistered<InnerPageController>() == true) {
          InnerPageController.to.refreshIndexTabbarList(index: 1);
        }
      } else {
        index = state.dataList.indexOf(model);
        model?.hasLike = true;
        state.dataList.replaceRange(index, index + 1, [model]);
      }
    }
  }

  //跳转聊天
  toChat(AmorsFeedModel model) async {
    if (!UserService.to.isLogin) {
      UserService.to.loginTip();
      return;
    }
    if (model.modelId == null) {
      return;
    }
    //新用户没有填写基础信息
    Map<String, dynamic>? userLoginInfo =
        Get.find<SPService>().get(spLoginInfo) as Map<String, dynamic>?;
    if (userLoginInfo?['register'] == true) {
      await Get.toNamed(Routes.register);
    }
    Map? params = await AmorsApis.createSession(modelId: model.modelId!);
    if (params != null && params['sessionNo'] != null && Get.currentRoute.startsWith(Routes.tabs)) {
      ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.chat);
      ReportUtil.reportViews(
          page: ReportUtil.amor, action: ReportUtil.go, value: ReportUtil.chatwin);
      Analytics().logEvent(Analytics.clickChar,
          visualTag: model.visualTag, charID: model.modelId, screen: Analytics.pageAmors);
      Get.toNamed(Routes.session, arguments: params['sessionNo']);
    }
  }

  //展示搜索
  showSearchWidget() async {
    ReportUtil.reportEvents(page: ReportUtil.amor, action: ReportUtil.search);
    AmorsSearchController controller = Get.put(AmorsSearchController(), permanent: true);
    Future.delayed(const Duration(milliseconds: 100), () => controller.searchAnimated.value = true);
    state.showSearchWidget = true;
    await Get.dialog(
      const AmorSearchWidget(),
      useSafeArea: false,
      transitionDuration: const Duration(milliseconds: 100),
    );
    Get.delete<AmorsSearchController>(force: true);
    Future.delayed(const Duration(milliseconds: 500), () {
      state.showSearchWidget = false;
    });
  }

  //刷新喜欢状态
  likeRequestRefresh({int? modelId, required bool like}) async {
    if (modelId == null) {
      return;
    }
    // for (var list in modelList) {
    //   for (var i = 0; i < list.length; i++) {
    //     if (list.elementAt(i).modelId == modelId) {
    //       likeOperation(model: list.elementAt(i), index: i);
    //       return;
    //     }
    //   }
    // }
  }

  //刷新角色列表
  refreshdataList() {
    state.clearList = true;
    selectedCategory(state.selectedCategoryIndex.value, loadmore: false, refresh: true);
  }

  //加载更多角色列表
  loadMoredataList() {
    state.discoverPage++;
    selectedCategory(state.selectedCategoryIndex.value, loadmore: true);
  }

  //用户切换时刷新列表
  refreshPage() {
    state.discoverPage = 1;
    state.selectedCategoryIndex.value = 0;
    state.clearList = true;
    state.dataList.clear();
    selectedCategory(0);
  }

  //滑动曝光监听
  void scrollObserver({required int index}) {
    if (Get.currentRoute != Routes.tabs || state.showSearchWidget == true) {
      return;
    }
    if (state.lastItemindex == index ||
        (state.tagsList.elementAt(state.selectedCategoryIndex.value) as AmorsCategoryModel).id !=
            -2) {
      return;
    }
    if (state.lastItemindex > index) {
      if (index < 2) {
        exposureEvent(model: state.dataList.elementAt(index));
      }
      exposureEvent(model: state.dataList.elementAt(index + 2));
    } else {
      if (index > 1) {
        exposureEvent(model: state.dataList.elementAt(index - 2));
        if (index > state.dataList.length - 3) {
          exposureEvent(model: state.dataList.elementAt(index));
        }
        Analytics().logEvent(Analytics.seeChar,
            visualTag: (state.dataList.elementAt(index) as AmorsFeedModel).visualTag,
            charID: (state.dataList.elementAt(index) as AmorsFeedModel).modelId,
            screen: Analytics.pageAmors);
      }
    }
    state.lastItemindex = index;
  }

  //角色曝光
  exposureEvent({required AmorsFeedModel model}) {
    if (model.modelId == null) {
      return;
    }
    if ((state.tagsList.elementAt(state.selectedCategoryIndex.value) as AmorsCategoryModel).id ==
            -2 &&
        state.exposureData.keys.contains('${model.modelId}') == false &&
        UserService.to.isLogin == true) {
      Get.find<SPService>().set(spExposureModel, model.modelId ?? 0);
      state.exposureData['${model.modelId}'] = CommonUtil.currentTimeMillis();
    }
  }

  //展示分类列表
  showTagView() async {
    // SerciceReportUtil.reportEvents(page: SerciceReportUtil.avas, action: SerciceReportUtil.theme);
    Map? result = await Get.dialog(
      TagsSelectWidget(
        tagsList: state.tagsList,
        selectedTagsIndex: [state.selectedCategoryIndex.value],
        tagSwitchType: state.tagSwitchType,
        multiple: false,
        backBtn: 'right',
      ),
      useSafeArea: false,
      transitionDuration: const Duration(milliseconds: 100),
    );
    if (result != null &&
        (result['selectedTagsIndex'] as List).isNotEmpty &&
        state.selectedCategoryIndex.value != result['selectedTagsIndex'][0]) {
      state.selectedCategoryIndex.value = result['selectedTagsIndex'][0];
      state.clearList = true;
      state.dataList.clear();
      selectedCategory(result['selectedTagsIndex'][0]);
    }
  }

  //顶部标题点击
  onTapTitle(int index) {
    if (index == 3) {
      AppService.sp.set(spHomeMomentsOntap, true);
      Analytics().logEvent(Analytics.cmoment);
    }
    // if (index == 4) {
    //   AppService.sp.set(spHomeCreateOntap, true);
    //   Analytics().logEvent(Analytics.ccreate);
    // }
    if (index == 2) {
      Analytics().logEvent(Analytics.cvideochat);
    }
    state.topMenuIndex.value = index;
  }

  @override
  void onReady() {
    super.onReady();
    //上报view事件
    ReportUtil.reportViews(page: ReportUtil.amor, action: ReportUtil.view);
  }

  @override
  void onClose() {
    super.onClose();
    discoverRefreshCtl.dispose();
  }
}
