import 'dart:convert';

class AiStylesModel {
  final String? name;
  final String? style;
  final String? url;
  final String? price;
  final String? icon;

  AiStylesModel({
    this.name,
    this.style,
    this.url,
    this.price,
    this.icon,
  });

  factory AiStylesModel.fromRawJson(String str) => AiStylesModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiStylesModel.fromJson(Map<String, dynamic> json) => AiStylesModel(
        name: json["name"],
        style: json["style"],
        url: json["url"],
        price: json["price"],
        icon: json["icon"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "style": style,
        "url": url,
        "price": price,
        "icon": icon,
      };
}
