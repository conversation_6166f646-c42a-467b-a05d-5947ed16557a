import 'dart:convert';

class AiUploadRes {
  final int? estimatedTime;
  final String? uid;

  AiUploadRes({
    this.estimatedTime,
    this.uid,
  });

  factory AiUploadRes.fromRawJson(String str) => AiUploadRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiUploadRes.fromJson(Map<String, dynamic> json) => AiUploadRes(
        estimatedTime: json['estimated_time'],
        uid: json['uid'],
      );

  Map<String, dynamic> toJson() => {
        'estimated_time': estimatedTime,
        'uid': uid,
      };
}

class AiResultRes {
  final List<String>? results;
  final int? status;
  final String? uid;

  AiResultRes({
    this.results,
    this.status,
    this.uid,
  });

  factory AiResultRes.fromRawJson(String str) => AiResultRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiResultRes.fromJson(Map<String, dynamic> json) => AiResultRes(
        results: json['results'] == null ? [] : List<String>.from(json['results']!.map((x) => x)),
        status: json['status'],
        uid: json['uid'],
      );

  Map<String, dynamic> toJson() => {
        'results': results == null ? [] : List<dynamic>.from(results!.map((x) => x)),
        'status': status,
        'uid': uid,
      };
}

class AiVideoResult {
  AiVideoResItem? item;

  AiVideoResult({
    this.item,
  });

  factory AiVideoResult.fromRawJson(String str) => AiVideoResult.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiVideoResult.fromJson(Map<String, dynamic> json) => AiVideoResult(
        item: json["item"] == null ? null : AiVideoResItem.fromJson(json["item"]),
      );

  Map<String, dynamic> toJson() => {
        "item": item?.toJson(),
      };
}

class AiVideoResItem {
  dynamic uid;
  int status;
  String resultPath;
  String imagePath;

  AiVideoResItem({
    required this.uid,
    required this.status,
    required this.resultPath,
    required this.imagePath,
  });

  factory AiVideoResItem.fromRawJson(String str) => AiVideoResItem.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiVideoResItem.fromJson(Map<String, dynamic> json) => AiVideoResItem(
        uid: json["uid"],
        status: json["status"],
        resultPath: json["resultPath"],
        imagePath: json["imagePath"],
      );

  Map<String, dynamic> toJson() => {
        "uid": uid,
        "status": status,
        "resultPath": resultPath,
        "imagePath": imagePath,
      };
}
