import 'dart:convert';

class AiHistroyModel {
  final int? id;
  final String? url;

  AiHistroyModel({
    this.id,
    this.url,
  });

  factory AiHistroyModel.fromRawJson(String str) => AiHistroyModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AiHistroyModel.fromJson(Map<String, dynamic> json) => AiHistroyModel(
        id: json['id'],
        url: json['url'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'url': url,
      };
}
