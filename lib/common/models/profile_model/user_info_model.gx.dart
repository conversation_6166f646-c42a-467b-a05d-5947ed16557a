// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserInfo _$UserInfoFromJson(Map<String, dynamic> json) => UserInfo(
      avatarUrl: json['avatarUrl'] as String?,
      numberOfTimes: json['numberOfTimes'] as int?,
      vipStatus: json['vipStatus'] as int?,
      endDate: json['endDate'] as int?,
      nickName: json['nickName'] as String?,
      birthday: json['birthday'] as String?,
      gender: json['gender'] as int?,
      intro: json['intro'] as String?,
      shareCode: json['shareCode'] as String?,
      vipType: json['vipType'] as String?,
      residueChatCount: json['residueChatCount'] as String?,
      gems: json['gems'] as int?,
      showGemTip: json['showGemTip'] as bool?,
      // lotoInfo: const _LotoInfoModelConverter().fromJson(jsonEncode(json['lotoInfo'])),
      unreadCount: json['unreadCount'] as int?,
      expiringGemsCount: json['expiringGemsCount'] as int?,
      actionable: json['actionable'] as int?,
      onGoing: json['onGoing'] as int?,
      available: json['available'] as int?,
      autoTranslate: json['autoTranslate'] as bool?,
      sourceLanguage: json['sourceLanguage'] as String?,
      targetLanguage: json['targetLanguage'] as String?,
      createImg: json['createImg'] as int?,
      createVideo: json['createVideo'] as int?,
    );

Map<String, dynamic> _$UserInfoToJson(UserInfo instance) => <String, dynamic>{
      'avatarUrl': instance.avatarUrl,
      'endDate': instance.endDate,
      'nickName': instance.nickName,
      'numberOfTimes': instance.numberOfTimes,
      'vipStatus': instance.vipStatus,
      'birthday': instance.birthday,
      'gender': instance.gender,
      'intro': instance.intro,
      'shareCode': instance.shareCode,
      'vipType': instance.vipType,
      'residueChatCount': instance.residueChatCount,
      'gems': instance.gems,
      'unreadCount': instance.unreadCount,
      'showGemTip': instance.showGemTip,
      'expiringGemsCount': instance.expiringGemsCount,
      'actionable': instance.actionable,
      'onGoing': instance.onGoing,
      'available': instance.available,
      'targetLanguage': instance.targetLanguage,
      'sourceLanguage': instance.sourceLanguage,
      'autoTranslate': instance.autoTranslate,
      'createImg': instance.createImg,
      'createVideo': instance.createVideo,
      // 'lotoInfo': const _LotoInfoModelConverter().toJson(instance.lotoInfo),
    };

LotoInfoModel _$LotoInfoModelFromJson(Map<String, dynamic> json) => LotoInfoModel(
      btnClick: json['btnClick'] as int?,
      btnExpire: json['btnExpire'] as int?,
      drawTime: json['drawTime'] as int?,
      gems: json['gems'] as int?,
      levelLabel: json['levelLabel'] as String?,
      lotoState: json['lotoState'] as int?,
      signInState: json['signInState'] as int?,
      tickets: json['tickets'] as int?,
      claimGems: json['claimGems'] as int?,
      winrates: (json['winrates'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$LotoInfoModelToJson(LotoInfoModel instance) => <String, dynamic>{
      'btnClick': instance.btnClick,
      'btnExpire': instance.btnExpire,
      'drawTime': instance.drawTime,
      'gems': instance.gems,
      'lotoState': instance.lotoState,
      'signInState': instance.signInState,
      'tickets': instance.tickets,
      'winrates': instance.winrates,
      'levelLabel': instance.levelLabel,
      'claimGems': instance.claimGems,
    };
