// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PurchaseConfigModel _$PurchaseConfigModelFromJson(Map<String, dynamic> json) =>
    PurchaseConfigModel(
      vip: json['vip'] == null
          ? null
          : VipTypeItem.fromJson(json['vip'] as Map<String, dynamic>),
      ssvip: json['ssvip'] == null
          ? null
          : VipTypeItem.fromJson(json['ssvip'] as Map<String, dynamic>),
      gem: json['gem'] == null
          ? null
          : VipTypeItem.fromJson(json['gem'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PurchaseConfigModelToJson(
        PurchaseConfigModel instance) =>
    <String, dynamic>{
      'ssvip': instance.ssvip,
      'vip': instance.vip,
      'gem': instance.gem,
    };

VipTypeItem _$VipTypeItemFromJson(Map<String, dynamic> json) => VipTypeItem(
      recharges: const _PurchaseItemModelConverter()
          .fromJson(jsonEncode(json['recharges'])),
      discounts: json['discounts'] as bool?,
      discountPrice: json['discountPrice'] as int?,
    );

Map<String, dynamic> _$VipTypeItemToJson(VipTypeItem instance) =>
    <String, dynamic>{
      'discounts': instance.discounts,
      'discountPrice': instance.discountPrice,
      'recharges':
          const _PurchaseItemModelConverter().toJson(instance.recharges),
    };

PurchaseItemModel _$PurchaseItemModelFromJson(Map<String, dynamic> json) =>
    PurchaseItemModel(
      title: json['title'] as String?,
      commend: json['commend'] as bool?,
      finalPrice: (json['finalPrice'] as num?)?.toDouble(),
      goodsNo: json['goodsNo'] as String?,
      num: json['num'] as int?,
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      rechargeType: json['rechargeType'] as String?,
      des: json['des'] as String?,
      showCountDown: json['showCountDown'] as bool?,
      countdown: json['countdown'] as int?,
      selected: json['selected'] as bool?,
      save: json['save'] as String?,
      tag: json['tag'] as String?,
      gems: json['gems'] as int?,
      discountPrice: (json['discountPrice'] as num?)?.toDouble(),
      unitPrice: json['unitPrice'] as String?,
      subscriptionCycle: json['subscriptionCycle'] as String?,
      subscriptionType: json['subscriptionType'] as String?,
      hot: json['hot'] as bool?,
      createImg: json['createImg'] as int?,
      createVideo: json['createVideo'] as int?,
    );

Map<String, dynamic> _$PurchaseItemModelToJson(PurchaseItemModel instance) =>
    <String, dynamic>{
      'commend': instance.commend,
      'finalPrice': instance.finalPrice,
      'goodsNo': instance.goodsNo,
      'num': instance.num,
      'originalPrice': instance.originalPrice,
      'discountPrice': instance.discountPrice,
      'rechargeType': instance.rechargeType,
      'title': instance.title,
      'des': instance.des,
      'showCountDown': instance.showCountDown,
      'countdown': instance.countdown,
      'tag': instance.tag,
      'unitPrice': instance.unitPrice,
      'selected': instance.selected,
      'save': instance.save,
      'gems': instance.gems,
      'subscriptionCycle': instance.subscriptionCycle,
      'subscriptionType': instance.subscriptionType,
      'hot': instance.hot,
      'createImg': instance.createImg,
      'createVideo': instance.createVideo,
    };
