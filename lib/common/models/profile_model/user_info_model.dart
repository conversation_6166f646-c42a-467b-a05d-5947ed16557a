import 'package:json_annotation/json_annotation.dart';

part 'user_info_model.gx.dart';

//flutter packages pub run build_runner build
@JsonSerializable()
class UserInfo {
  String? avatarUrl;
  int? endDate;
  String? nickName;
  int? numberOfTimes;
  int? vipStatus; //是否过期
  String? birthday;
  int? gender;
  String? intro;
  String? shareCode;
  String? vipType; //VIP状态
  String? residueChatCount;
  int? gems;
  int? unreadCount;
  bool? showGemTip;
  int? expiringGemsCount;
  int? actionable;
  int? onGoing;
  int? available;
  bool? autoTranslate;
  String? sourceLanguage;
  String? targetLanguage;
  int? createImg;
  int? createVideo;
  // @_LotoInfoModelConverter()
  // LotoInfoModel? lotoInfo;
  UserInfo({
    this.avatarUrl,
    this.numberOfTimes,
    this.vipStatus,
    this.endDate,
    this.nickName,
    this.birthday,
    this.gender,
    this.intro,
    this.shareCode,
    this.vipType,
    this.residueChatCount,
    this.gems,
    this.unreadCount,
    this.showGemTip,
    this.expiringGemsCount,
    this.actionable,
    this.available,
    this.onGoing,
    this.autoTranslate,
    this.sourceLanguage,
    this.targetLanguage,
    this.createImg,
    this.createVideo,
  });
  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

@JsonSerializable()
class LotoInfoModel {
  int? btnClick;
  int? btnExpire;
  int? drawTime;
  int? gems;
  int? lotoState;
  int? signInState;
  int? tickets;
  int? claimGems;
  double? winrates;
  String? levelLabel;
  LotoInfoModel({
    this.btnClick,
    this.btnExpire,
    this.drawTime,
    this.gems,
    this.levelLabel,
    this.lotoState,
    this.signInState,
    this.tickets,
    this.winrates,
    this.claimGems,
  });
  factory LotoInfoModel.fromJson(Map<String, dynamic> json) => _$LotoInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$LotoInfoModelToJson(this);
}
