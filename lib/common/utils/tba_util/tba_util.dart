import 'dart:convert';
import 'dart:io';

import 'package:adjust_sdk/adjust.dart';
import 'package:advertising_id/advertising_id.dart';
import 'package:amor_app/common/apis/apis_path/apis_path.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:android_play_install_referrer/android_play_install_referrer.dart';
// import 'package:android_package_manager/android_package_manager.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart' as getx;
import 'package:public_ip_address/public_ip_address.dart';
import 'package:uuid/uuid.dart';
// import 'package:android_id/android_id.dart';

class TbaUtil {
  // 连接超时时间
  static const int connectTimeout = 1 * 10000;
  // 响应超时时间
  static const int receiveTimeout = 1 * 10000;
  static TbaUtil? _instance;
  Dio _dio = Dio();
  Dio get dio => _dio;

  int? zone_offset;
  String? bundle_id;
  String? ip;
  String? device_model;
  String? ab_test;
  String? app_version;
  String? operator;
  String? system_language;
  String? manufacturer;
  String? screen_dpi;
  String? distinct_id;
  String? log_id;
  String? android_id;
  String? idfa;
  String? os;
  String? os_country;
  String? client_ts;
  String? gaid;
  String? channel;
  String? key;
  String? network_type;
  String? os_version;
  String? brand;
  String? idfv;
  String? build;
  String? lat;
  String? sdk_ver;
  var uuidUtil = const Uuid();
  //cloak 请求次数
  int cloakRequestCount = 0;
  //cloak是否命中
  bool cloakHit = true;
  TbaUtil._internal() {
    _instance = this;
  }

  factory TbaUtil() => _instance ?? TbaUtil._internal();

  init() async {
    SPService sp = getx.Get.find<SPService>();
    zone_offset = DateTime.now().timeZoneOffset.inHours;
    ip = await IpAddress().getIp();
    if (ip != null && ip!.length > 30) {
      ip = null;
    }
    system_language = getx.Get.locale.toString();
    double screenWidth = 0;
    //getx.Get.width;
    double screenHeight = 0;
    // getx.Get.height;
    double devicePixelRatio = getx.Get.pixelRatio;
    double canvasWidth = screenWidth * devicePixelRatio;
    double canvasHeight = screenHeight * devicePixelRatio;
    screen_dpi = '${canvasWidth}x$canvasHeight';
    distinct_id = (sp.get(spDeviceId) ?? '').toString();
    os_country = getx.Get.locale?.countryCode;
    if (sp.get(spAppInfo) != null) {
      final packageModel = PackageInfoModel.fromJson(sp.get(spAppInfo) as Map<String, dynamic>);
      bundle_id = packageModel.packageName;
      app_version = packageModel.version;
      build = packageModel.buildNumber;
    }
    if (sp.get(spDeviceInfo) != null) {
      final devideModel = DeviceInfoModel.fromJson(sp.get(spDeviceInfo) as Map<String, dynamic>);
      device_model = devideModel.brand;
      manufacturer = devideModel.manufacturer;
      os_version = '${devideModel.platform} ${devideModel.systemVersion}';
      brand = devideModel.manufacturer;
    }
    if (Platform.isIOS) {
      Adjust.getIdfv().then((value) {
        idfv = value;
      });
      os = 'afire';
    } else {
      // const androidIdPlugin = AndroidId();
      // android_id = await androidIdPlugin.getId();
      os = 'brink';
      gaid = idfa;
    }
    BaseOptions options = BaseOptions(
      connectTimeout: const Duration(milliseconds: connectTimeout),
      receiveTimeout: const Duration(milliseconds: receiveTimeout),
      contentType: 'application/json',
      headers: Platform.isAndroid
          ? {'musket': uuidUtil.v1(), 'withy': device_model ?? '', 'wilt': idfv ?? ''}
          : {'snarl': device_model ?? '', 'capo': uuidUtil.v1(), 'ossify': ip ?? ''},
    );

    /// 初始化dio
    _dio = Dio(options);

    ///日志打印
    _dio.interceptors.add(LogInterceptor(responseBody: true));
    installEvent();
    tbaCloakRequest();
  }

  ///TBA请求
  Future<Map<String, dynamic>> tbaEventRequest(
      {Map<String, dynamic>? eventParam, String? eventParamKey, String? queryParameters}) async {
    Map<String, dynamic> param = commonParam();
    String queryString = Platform.isAndroid
        ? '?ababa=${Uri.encodeComponent(client_ts ?? '')}&coventry=${Uri.encodeComponent(distinct_id ?? '')}'
        : '?spoilage=${Uri.encodeComponent(app_version ?? '')}&machismo=${Uri.encodeComponent('')}&reedbuck=${Uri.encodeComponent(network_type ?? '')}&gogo=${Uri.encodeComponent(operator ?? '')}';
    if (queryParameters != null) {
      queryString = '$queryString&$queryParameters';
    }
    String baseUrl = Platform.isAndroid ? Api.tbaAndroidRegularUrl : Api.tbaIOSRegularUrl;
    String path = '$baseUrl$queryString';
    if (eventParam != null && eventParamKey != null) {
      param[eventParamKey] = eventParam;
    }
    //安装事件参数放到外层的json对象中
    if (eventParam != null && eventParamKey == null) {
      param.addAll(eventParam);
    }
    // print('TBA param:');
    // print(param);
    Response response = await dio.post(path, data: param);
    //保存失败事件
    if (response.statusCode != 200) {
      debugPrint('事件上报失败');
      List falseList = (getx.Get.find<SPService>().get(spTbEventfail) ?? []) as List;
      List list = falseList.map((e) => e).toList();
      list.add(jsonEncode({'path': path, 'param': param}));
      getx.Get.find<SPService>().set(spTbEventfail, list);
    } else {
      debugPrint('事件上报成功');
    }
    //重试未上报成功事件
    retryFailEvent();
    return {'statusCode': response.statusCode, 'eventParam': param};
  }

  //公共参数
  Map<String, dynamic> commonParam() {
    Map<String, dynamic> param = {};

    log_id = uuidUtil.v1();
    key = uuidUtil.v1();
    client_ts = CommonUtil.currentTimeMillis().toString();
    setNetworkType();
    if (Platform.isAndroid) {
      Map<String, dynamic> ossifyParam = {};
      Map<String, dynamic> pomonaParam = {};
      Map<String, dynamic> perseidParam = {};
      Map<String, dynamic> diademParam = {};
      ossifyParam['boundary'] = operator ?? '';
      ossifyParam['rear'] = ip;
      pomonaParam['gaslight'] = bundle_id;
      pomonaParam['musket'] = log_id;
      pomonaParam['withy'] = device_model;
      pomonaParam['radical'] = os_version;
      pomonaParam['paz'] = os_country;
      pomonaParam['gop'] = gaid;
      perseidParam['antoine'] = zone_offset;
      perseidParam['delia'] = system_language;
      perseidParam['tabular'] = os;
      perseidParam['ababa'] = client_ts;
      perseidParam['choline'] = network_type;
      diademParam['freeboot'] = brand;
      diademParam['teddy'] = android_id ?? '';
      diademParam['coventry'] = distinct_id;
      diademParam['obligate'] = manufacturer ?? '';
      diademParam['knight'] = app_version;
      param['ossify'] = ossifyParam;
      param['pomona'] = pomonaParam;
      param['perseid'] = perseidParam;
      param['diadem'] = diademParam;
    } else {
      param['snarl'] = device_model;
      param['stroke'] = idfv;
      param['applause'] = os;
      param['gogo'] = operator ?? '';
      param['ossify'] = ip;
      param['del'] = client_ts;
      param['horseman'] = distinct_id;
      param['spoilage'] = app_version;
      param['platte'] = os_version;
      param['plugging'] = bundle_id;
      param['capo'] = log_id;
      param['hornwort'] = manufacturer ?? '';
      param['illusive'] = system_language;
    }
    return param;
  }

  //install事件
  installEvent() async {
    sessionEvent();
    if (getx.Get.find<SPService>().get(spInstallEvent) != null) {
      // retryFailEvent();
      return;
    }
    Map<String, dynamic> installEventParam = {};
    //获取userAgent
    // await FkUserAgent.init();
    // String? userAgent = FkUserAgent.webViewUserAgent;
    bool? isLimitAdTrackingEnabled;
    try {
      isLimitAdTrackingEnabled = await AdvertisingId.isLimitAdTrackingEnabled;
    } on PlatformException {
      isLimitAdTrackingEnabled = false;
    }
    if (isLimitAdTrackingEnabled == true) {
      lat = Platform.isAndroid ? 'innard' : 'fantasy';
    } else {
      lat = Platform.isAndroid ? 'tabletop' : 'cattail';
    }
    if (Platform.isAndroid) {
      ReferrerDetails? referrerDetails;
      try {
        referrerDetails = await AndroidPlayInstallReferrer.installReferrer;
      } catch (e) {}
      // final pm = AndroidPackageManager();
      // PackageInfo? packageInfo = await pm.getPackageInfo(packageName: packageName);
      installEventParam['enfant'] = 'build/$build';
      if (referrerDetails != null) {
        installEventParam['gloat'] = referrerDetails.installReferrer;
        installEventParam['cameron'] = referrerDetails.installVersion;
        installEventParam['sweetie'] = '';
        installEventParam['office'] = referrerDetails.referrerClickTimestampSeconds;
        installEventParam['circuit'] = referrerDetails.installBeginTimestampSeconds;
        installEventParam['pomology'] = referrerDetails.referrerClickTimestampServerSeconds;
        installEventParam['symposia'] = referrerDetails.installBeginTimestampServerSeconds;
      }
      installEventParam['bulldog'] = 0;
      installEventParam['barley'] = 0;
      installEventParam['pearce'] = lat;
      installEventParam['inchworm'] = 'scenario';
    } else {
      installEventParam['populate'] = 'build/$build';
      installEventParam['ehrlich'] = ''; //user_agent
      installEventParam['ywca'] = lat;
      installEventParam['medic'] = 0;
      installEventParam['pirogue'] = 0;
      installEventParam['ganglion'] = 0;
      installEventParam['arrow'] = 0;
      installEventParam['rep'] = 0;
      installEventParam['material'] = 0;
    }
    Map result = await tbaEventRequest(eventParam: installEventParam, eventParamKey: null);
    //成功
    if (result['statusCode'] == 200) {
      getx.Get.find<SPService>().set(spInstallEvent, true);
    }
  }

  //session事件
  sessionEvent() async {
    // await tbaEventRequest(eventParamKey: 'potsherd', eventParam: {});
  }

  //重试失败的事件
  retryFailEvent() async {
    List failList = (getx.Get.find<SPService>().get(spTbEventfail) ?? []) as List;
    List list = failList.map((e) => jsonDecode(e)).toList();
    for (var i = 0; i < list.length; i++) {
      Map failData = list[i];
      Response response = await dio.post(failData['path'], data: failData['param']);
      if (response.statusCode == 200) {
        list.replaceRange(i, i + 1, ['success']);
      }
    }
    list.removeWhere((element) => element == 'success');
    getx.Get.find<SPService>().set(spTbEventfail, list);
  }

  //Cloak
  tbaCloakRequest() async {
    if (kDebugMode) {
      AppService.audit = false;
      return;
    }
    if (cloakRequestCount > 1 || AppService.audit == false) {
      return;
    }
    String baseUrl = Platform.isIOS ? Api.tbaIOSCloakUrl : Api.tbaAndroidCloakUrl;

    String path = baseUrl;
    Map<String, dynamic> param = {};
    client_ts = CommonUtil.currentTimeMillis().toString();
    setNetworkType();
    if (Platform.isAndroid) {
      param['gaslight'] = Uri.encodeComponent(bundle_id ?? '');
      param['tabular'] = Uri.encodeComponent(os ?? '');
      param['knight'] = Uri.encodeComponent(app_version ?? '');
      param['coventry'] = Uri.encodeComponent(distinct_id ?? '');
      param['ababa'] = Uri.encodeComponent(client_ts ?? '');
      param['withy'] = Uri.encodeComponent(device_model ?? '');
      param['radical'] = Uri.encodeComponent(os_version ?? '');
      param['gop'] = Uri.encodeComponent(gaid ?? '');
      param['teddy'] = Uri.encodeComponent(android_id ?? '');
      param['choline'] = Uri.encodeComponent(network_type ?? '');
      /**
       * 命中黑名单:wretch
       * 正常模式:swindle
       */
      Response response = await dio.get(path, queryParameters: param);
      if (response.statusCode == 200 && response.data == 'swindle') {
        cloakHit = false;
        await AppService.sp.set(spFromDeepLink, true);
        AppService.setAuditMode();
      } else {
        cloakHit = true;
      }
    } else {
      param['plugging'] = Uri.encodeComponent(bundle_id ?? '');
      param['applause'] = Uri.encodeComponent(os ?? '');
      param['spoilage'] = Uri.encodeComponent(app_version ?? '');
      param['horseman'] = Uri.encodeComponent(distinct_id ?? '');
      param['del'] = Uri.encodeComponent(client_ts ?? '');
      param['snarl'] = Uri.encodeComponent(device_model ?? '');
      param['platte'] = Uri.encodeComponent(os_version ?? '');
      param['maid'] = Uri.encodeComponent(brand ?? '');
      param['inimical'] = Uri.encodeComponent(idfa ?? '');
      param['stroke'] = Uri.encodeComponent(idfv ?? '');
      param['ossify'] = Uri.encodeComponent(ip ?? '');
      param['reedbuck'] = Uri.encodeComponent(network_type ?? '');
      /**
       * 命中黑名单:flourish
       * 正常模式:sial
       */
      Response response = await dio.get(path, queryParameters: param);
      if (response.statusCode == 200 && response.data == 'sial') {
        cloakHit = false;
        await AppService.sp.set(spFromDeepLink, true);
        AppService.setAuditMode();
      }
    }
  }

  //app自定义埋点
  static const String actionView = 'view';
  static const String actionSelect = 'select';
  static const String actionSubscribe = 'subscribe';
  static const String actionGet = 'get';
  static const String actionFinish = 'finish';
  static const String pageChatwin = 'chatwin';
  static const String pageCards = 'cards';
  static const String pageAmor = 'amor';
  static const String pageShortgem = 'shortgem';
  static const String pageKyc = 'kyc';
  static const String pageHot = 'hot';
  static const String pageGem = 'gem';

  appEvent({required String eventName, String? eventValue, String? fromPage, String? page}) async {}

  //设置网络状态
  setNetworkType() {
    if (NetworkUtil().lastConnectResult == ConnectivityResult.wifi) {
      network_type = 'wifi';
    } else if (NetworkUtil().lastConnectResult == ConnectivityResult.ethernet) {
      network_type = 'ethernet';
    } else if (NetworkUtil().lastConnectResult == ConnectivityResult.bluetooth) {
      network_type = 'bluetooth';
    } else if (NetworkUtil().lastConnectResult == ConnectivityResult.mobile) {
      network_type = 'mobile';
    } else if (NetworkUtil().lastConnectResult == ConnectivityResult.none) {
      network_type = 'none';
    }
  }
}
