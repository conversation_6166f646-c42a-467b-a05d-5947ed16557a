import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/common_model/common_model.dart';
import 'package:amor_app/common/models/profile_model/purchase_config_model.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_sku_ctr.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/controller.dart';
import 'package:amor_app/pages/session/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../utils.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
// import 'package:in_app_purchase_android/in_app_purchase_android.dart';
// import 'package:in_app_purchase_android/src/billing_client_wrappers/billing_client_wrapper.dart';

late InAppPurchase _inAppPurchase;
late StreamSubscription<List<PurchaseDetails>> _subscription;
bool isAvailable = false;
List<ProductDetails> products = <ProductDetails>[];
//正在购买的订阅商品是否有优惠
int? subscribeOffer;
//正在购买的商品
ProductDetails? selectedProductDetails;
bool restore = false;
String orderNo = '';

class InAppPurchaseUtil {
  static final InAppPurchaseUtil _instance = InAppPurchaseUtil._internal();

  factory InAppPurchaseUtil() => _instance;

  //初始化
  InAppPurchaseUtil._internal() {
    _inAppPurchase = InAppPurchase.instance;
    final Stream<List<PurchaseDetails>> purchaseUpdated = _inAppPurchase.purchaseStream;
    _subscription = purchaseUpdated.listen((List<PurchaseDetails> purchaseDetailsList) {
      _listenToPurchaseUpdated(purchaseDetailsList);
    }, onDone: () {
      debugPrint('监听取消');
      _subscription.cancel();
    }, onError: (Object error) {
      debugPrint('监听错误');
    });
  }

  //检查购买
  void checkRestorePurchases() async {
    debugPrint('恢复购买');
    restore = true;
    //如果有已支付未消费的订单 会走回调
    await _inAppPurchase.restorePurchases();
    Loading.dismiss();
  }

  //初始化商店
  Future<bool> initStoreInfo() async {
    isAvailable = await _inAppPurchase.isAvailable();
    selectedProductDetails = null;
    return isAvailable;
  }

  //监听
  Future<void> _listenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    if (restore == true) {
      if (purchaseDetailsList.isEmpty) {
        Loading.toast('No record found');
      } else {
        // workData(purchaseDetailsList);
        debugPrint('restore');
        // //暂时只处理一个
        final bool valid = await _verifyPurchase(purchaseDetailsList.last);
        if (valid) {
          deliverProduct(purchaseDetailsList.last);
        } else {
          _handleInvalidPurchase(purchaseDetailsList.last);
        }
        if (purchaseDetailsList.last.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetailsList.last);
        }
      }
      return;
    }

    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint('收到回调：${purchaseDetails.status}');
      //不处理非当前购买的订单
      if (selectedProductDetails == null) {
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
        break;
      }
      if (purchaseDetails.status == PurchaseStatus.pending) {
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          handleError(purchaseDetails.error);
        } else if (purchaseDetails.status == PurchaseStatus.restored) {
          if (restore == true) {}
        } else if (purchaseDetails.status == PurchaseStatus.purchased) {
          debugPrint('已支付');
          final bool valid = await _verifyPurchase(purchaseDetails);
          if (valid) {
            deliverProduct(purchaseDetails);
          } else {
            _handleInvalidPurchase(purchaseDetails);
          }
        } else if (purchaseDetails.status == PurchaseStatus.canceled) {
          handleError(IAPError(source: '', code: '', message: 'canceled'));
        }
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

//查询商品详情列表
  Future<List<AppPurchaseModel>> queryProduct(
      {required List<PurchaseItemModel> serviceModelList, bool loading = true}) async {
    if (serviceModelList.isEmpty) {
      return [];
    }
    if (loading == true) {
      Loading.show();
    }
    List<AppPurchaseModel> resultList = [];
    List<String> goodsId = [];
    for (var element in serviceModelList) {
      goodsId.add(element.goodsNo!);
    }
    List<AppPurchaseModel> list =
        await getProducts(productId: goodsId.toSet(), serviceModelList: serviceModelList);
    if (loading == true) {
      Loading.dismiss();
    }
    list.removeWhere((value) => value.serviceProductDetails == null);
    resultList.addAll(list);
    //vip
    if (resultList.isNotEmpty && resultList.first.serviceProductDetails?.num != null) {
      resultList
          .sort((a, b) => a.serviceProductDetails!.num!.compareTo(b.serviceProductDetails!.num!));
    }
    //gem
    if (resultList.isNotEmpty && resultList.first.serviceProductDetails?.gems != null) {
      resultList
          .sort((a, b) => a.serviceProductDetails!.gems!.compareTo(b.serviceProductDetails!.gems!));
    }
    resultList.removeWhere((value) => value.productDetails == null);
    return resultList;
  }

  //获取单个商品的信息 包含是否有优惠
  Future<List<AppPurchaseModel>> getProducts(
      {required Set<String> productId, required List<PurchaseItemModel> serviceModelList}) async {
    orderNo = '';
    final ProductDetailsResponse productDetailResponse =
        await _inAppPurchase.queryProductDetails(productId);
    if (productDetailResponse.error != null || productDetailResponse.productDetails.isEmpty) {
      handleError(productDetailResponse.error);
      return [];
    }
    List<ProductDetails> products = productDetailResponse.productDetails;
    if (Platform.isAndroid) {
      Map<String, List<ProductDetails>> minPrice = {};
      for (var element in products) {
        if (minPrice.containsKey(element.id) == false) {
          minPrice[element.id] = [element];
        } else {
          minPrice[element.id]!.add(element);
        }
      }
      List<List<ProductDetails>> results = [];
      minPrice.forEach(
        (key, value) {
          if (value.length > 1) {
            value.sort((a, b) => a.rawPrice.compareTo(b.rawPrice));
          }
          results.add(value);
        },
      );
      return results.map((element) {
        PurchaseItemModel? serviceModel = serviceModelList.firstWhereOrNull((value) {
          return value.goodsNo == element.first.id;
        });
        return AppPurchaseModel(
            selected: serviceModel?.selected == true,
            serviceProductDetails: serviceModel,
            productDetails: element.last,
            offerProductDetails: element.length > 1 ? element.first : null,
            offerPrice: element.length > 1 ? element.first.rawPrice : null);
      }).toList();
    } else {
      return products.map((element) {
        SKProductWrapper skProduct = (element as AppStoreProductDetails).skProduct;
        PurchaseItemModel? serviceModel = serviceModelList.firstWhereOrNull((value) {
          return value.goodsNo == element.id;
        });
        return AppPurchaseModel(
            serviceProductDetails: serviceModel,
            productDetails: element,
            selected: serviceModel?.selected == true,
            offerPrice: skProduct.introductoryPrice != null //判断iOS 商品是否有优惠
                ? double.tryParse(skProduct.introductoryPrice!.price)
                : null);
      }).toList();
    }
  }

  //请求订阅 offer:是否有优惠
  fetchSubscribe({required ProductDetails productDetails, required bool offer}) async {
    Loading.show();
    creatOrder(
        goodsNo: productDetails.id,
        currency: productDetails.currencySymbol,
        payType: 'VIP',
        payAmount: productDetails.rawPrice);
    restore = false;
    subscribeOffer = offer == true ? 1 : 0;
    selectedProductDetails = productDetails;
    final packageModel =
        PackageInfoModel.fromJson(AppService.sp.get(spAppInfo) as Map<String, dynamic>);
    PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
        applicationUserName: jsonEncode({
          "sc": UserService.to.shareCode,
          "v": CommonUtil.getVersionWithNumStr(packageModel.version ?? '1.0.0')
        }));
    _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
  }

  //请求内购
  fetchPurchase({required ProductDetails productDetails}) async {
    Loading.show();
    creatOrder(
        goodsNo: productDetails.id,
        currency: productDetails.currencySymbol,
        payType: 'GEM',
        payAmount: productDetails.rawPrice);
    restore = false;
    subscribeOffer = 0;
    selectedProductDetails = productDetails;
    final packageModel =
        PackageInfoModel.fromJson(AppService.sp.get(spAppInfo) as Map<String, dynamic>);
    PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
        applicationUserName: jsonEncode({
          "sc": UserService.to.shareCode,
          "v": CommonUtil.getVersionWithNumStr(packageModel.version ?? '1.0.0')
        }));
    _inAppPurchase.buyConsumable(purchaseParam: purchaseParam);
  }

  //获取订单
  creatOrder(
      {required String goodsNo,
      required String currency,
      required String payType,
      required double payAmount}) async {
    orderNo = '';
    Map? result = await ProfileApis.orderCreate(
        goodsNo: goodsNo, currency: currency, payType: payType, payAmount: payAmount);
    if (result != null && result.containsKey('orderNo')) {
      orderNo = result['orderNo'];
    }
  }

//验证购买
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    String kPurchaseToken = purchaseDetails.verificationData.serverVerificationData;
    if (kPurchaseToken.isEmpty) {
      return Future<bool>.value(false);
    }
    Map<String, dynamic> param = {};
    if (Platform.isIOS) {
      param['serviceParam'] = {
        'transactionId': purchaseDetails.purchaseID,
        'receiptData': kPurchaseToken,
        'goodsNo': purchaseDetails.productID,
        'hasPromotion': subscribeOffer,
        'orderNo': orderNo,
        'type': getPurchasType(purchaseDetails.productID),
      };
    } else {
      Map verificationData = jsonDecode(purchaseDetails.verificationData.localVerificationData);
      param['serviceParam'] = {
        'goodsNo': verificationData['productId'],
        'purchaseToken': kPurchaseToken,
        'hasPromotion': subscribeOffer,
        'orderNo': orderNo,
        'type': getPurchasType(purchaseDetails.productID),
      };
    }
    //保存
    List list = List.from((AppService.sp.get(spInAppPurchaseOrder) as List?) ?? []);
    list.add(jsonEncode(param));
    await AppService.sp.set(spInAppPurchaseOrder, list);
    return Future<bool>.value(true);
  }

  //购买类型
  /// 1: gem
  /// 0: vip
  int getPurchasType(String productId) {
    return (productId == 'crazepack' ||
            productId == 'typicalpack' ||
            productId == 'wildpack' ||
            productId == '1000k' ||
            productId == '10k' ||
            productId == '50k')
        ? 1
        : 0;
  }

//服务器验证购买 刷新页面
  deliverProduct(PurchaseDetails purchaseDetails) {
    //未登录
    if (UserService.to.isLogin == false) {
      Loading.toast('Payment successful! Access benefits upon login');
      return;
    }
    if (Get.isRegistered<PurchasePageController>()) {
      PurchasePageController.to.verifyInAppPurchase();
    }
    selectedProductDetails = null;

     if (Get.isRegistered<AiSkuCtr>()) {
      AiSkuCtr.to.verifyInAppPurchase();
    }
  }

//购买无效
  void _handleInvalidPurchase(PurchaseDetails purchaseDetails) {
    handleError(null);
  }

//报错信息
  void handleError(IAPError? error) {
    Loading.dismiss();
    if (Get.isRegistered<PurchasePageController>() == true) {
      String toastStr = "Subscription failed😫 Amor awaits and can't wait to talk to you more.😘";
      /*
    if (Get.find<SPService>().get(kChatPageRoleName) != null) {
      String? roleName = Get.find<SPService>().get(kChatPageRoleName) as String;
      toastStr = "Subscription failed😫 $roleName awaits and can't wait to talk to you more.😘";
    }
      */
      Loading.toast(toastStr);
      if (orderNo.isNotEmpty) {
        ProfileApis.orderUpdateFail(orderNo: orderNo, errMsg: error?.message ?? '');
      }
      Analytics().logEvent(
        error?.message == 'canceled' ? Analytics.purchaseCancel : Analytics.purchaseFailed,
        itemPrice: selectedProductDetails?.rawPrice,
        currency: CurrencySymbol.currencySymbolMap[selectedProductDetails?.currencyCode] ?? '\$',
        sourceEvent: Analytics().hotSourceEvent,
        sourceChar: Get.isRegistered<SessionPageController>()
            ? SessionPageController.to.state.modelId.toString()
            : null,
        sourceScreen: Analytics().hotSourceScreen,
        screen: Analytics.pageHot,
        failSource: error?.source,
        failCode: error?.code,
        failMessage: error?.message,
        failDetail: error?.details,
      );
      debugPrint('支付错误：${error.toString()}');
    }
    selectedProductDetails = null;
  }

  //处理restore数据
  workData(List<PurchaseDetails> purchaseDetailsList) {
    if (purchaseDetailsList.isNotEmpty) {
      final ids = purchaseDetailsList.map(
        (e) {
          var detail = e as AppStorePurchaseDetails;
          return detail.skPaymentTransaction.originalTransaction?.transactionIdentifier;
        },
      ).toSet();
      //排序
      List<PurchaseDetails> sortedList = List.from(purchaseDetailsList);
      sortedList.sort((a, b) => (int.tryParse(b.transactionDate ?? '') ?? 0)
          .compareTo(int.tryParse(a.transactionDate ?? '') ?? 0));

      sortedList.retainWhere((x) {
        var detail = x as AppStorePurchaseDetails;

        return ids.remove(detail.skPaymentTransaction.originalTransaction?.transactionIdentifier);
      });
      setUplistenToPurchaseUpdated(sortedList);
    }
  }

  //商品回调   处理购买更新
  Future<void> setUplistenToPurchaseUpdated(List<PurchaseDetails> purchaseDetailsList) async {
    List<AppStorePurchaseDetails> detailsList = [];
    // 商品列表
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      var appstoreDetail = purchaseDetails as AppStorePurchaseDetails;
      // print(purchaseDetails);
      detailsList.add(appstoreDetail);
    }
    //提交服务器
    // loadAppleGetPayInfo(detailsList);
  }
}

class AppPurchaseModel {
  ProductDetails? productDetails;
  ProductDetails? offerProductDetails;
  double? offerPrice;
  bool? selected;
  int? num;
  PurchaseItemModel? serviceProductDetails;
  AppPurchaseModel({
    this.offerProductDetails,
    this.productDetails,
    this.offerPrice,
    this.selected,
    this.serviceProductDetails,
    this.num,
  });
}
