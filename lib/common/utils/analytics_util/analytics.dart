import 'package:amor_app/common/utils/utils.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';

class Analytics {
  static const String seeChar = 'see_char';
  static const String clickChar = 'click_char';
  static const String view = 'screen_view';
  static const String showLockedpic = 'show_lockedpic';
  static const String clickLockedpic = 'click_lockedpic';
  static const String viewLockedchar = 'view_lockedchar';
  static const String unlockHot = 'unlock_hot';
  static const String unlockAds = 'unlock_ads';
  static const String tailorcardHot = 'tailorcard_hot';
  static const String clickHot = 'click_hot';
  static const String clickStart2explore = 'click_start2explore';
  static const String clickClaim = 'click_claim';
  static const String clickDouble = 'click_double';
  static const String swipeL1 = 'swipe_l1';
  static const String swipeL2 = 'swipe_l2';
  static const String clickWeekly = 'click_weekly';
  static const String clickMonthly = 'click_monthly';
  static const String clickYearly = 'click_yearly';
  static const String clickSubscribe = 'click_subscribe';
  static const String clickUpgrade = 'click_upgrade';
  static const String purchaseCancel = 'purchase_cancel';
  static const String purchaseFailed = 'purchase_failed';
  static const String toast = 'toast';

  //screen
  static const String pageAmors = 'amors';
  static const String pageChatwin = 'chatwin';
  static const String pageCards = 'cards';
  static const String pageMine = 'mine';
  static const String pageAgesafety = 'agesafety';
  static const String pagePronounpage = 'pronounpage';
  static const String pageKyc = 'kyc';
  static const String pageRateguide = 'rateguide';
  static const String pageClaimpop = 'claimpop';
  static const String pageOutofgem = 'outofgem';
  static const String pageFinishclone = 'finishclone';
  static const String pageModifyclone = 'modifyclone';
  static const String pageHot = 'hot';
  static const String undress = 'undress';

  // V 1.1.0
  static const String clocktext = 'c_locktext';
  static const String clockpic = 'c_lockpic';
  static const String clockvideo = 'c_lockvideo';
  static const String clockaudio = 'c_lockaudio';
  static const String cunenter = 'c_un_enter';
  static const String tunpop = 't_un_pop';
  static const String cunupload = 'c_un_upload';
  static const String cungenerate = 'c_un_generate';
  static const String ungensuc = 'un_gen_suc';
  static const String ccall = 'c_call';
  static const String taicallyou = 't_aicallyou';
  static const String cmoment = 'c_moment';
  static const String cvideochatchar = 'c_videochat_char';
  static const String cundrchar = 'c_undr_char';
  static const String ccreate = 'c_create';
  static const String cvideochat = 'c_videochat';

  static const String cgift = 'c_gift';
  static const String giftclo = 'gift_clo';
  static const String gifttoy = 'gift_toy';

  static const String c_un_generate = 'c_un_generate';
  static const String un_gen_suc = 'un_gen_suc';

  static const String tvipundress = 't_vip_undress';
  static String sucgemsundress(String productId) => 'suc_gems_${productId}_undress';
  static String sucvipundress(String productId) => 'suc_vip_${productId}_undress';
  static const String tpaygemslocktext = 't_paygems_locktext';
  static String sucgemslocktext(String productId) => 'suc_gems_${productId}_locktext';
  static String sucviplocktext(String productId) => 'suc_vip_${productId}_locktext';
  static const String tpaygemslockpic = 't_paygems_lockpic';
  static String sucgemslockpic(String productId) => 'suc_gems_${productId}_lockpic';
  static String sucviplockpic(String productId) => 'suc_vip_${productId}_lockpic';
  static const String tpaygemslockvideo = 't_paygems_lockvideo';
  static String sucgemslockvideo(String productId) => 'suc_gems_${productId}_lockvideo';
  static String sucviplockvideo(String productId) => 'suc_vip_${productId}_lockvideo';
  static const String tpaygemslockaudio = 't_paygems_lockaudio';
  static String sucgemsaudio(String productId) => 'suc_gems_${productId}_audio';
  static String sucviplockaudio(String productId) => 'suc_vip_${productId}_lockaudio';
  static const String tpayvipcall = 't_vip_call';
  static String sucviplockcall(String productId) => 'suc_vip_${productId}_call';
  static const String tpayvipacceptcall = 't_vip_acceptcall';
  static String sucviplockacceptcall(String productId) => 'suc_vip_${productId}_acceptcall';
  //dev 只是用来判断上报订阅成功事件
  static const String devsucvippostpic = 'dev_sucvip_postpic';
  static String sucvippostpic(String productId) =>
      'suc_vip${AppService.audit == true ? 'a' : 'b'}_${productId}_postpic';
  static const String devsucvippostvideo = 'dev_sucvip_postvideo';
  static String sucvippostvideo(String productId) =>
      'suc_vip${AppService.audit == true ? 'a' : 'b'}_${productId}_postvideo';
  static const String devsucvipundrchar = 'dev_sucvip_undrchar';
  static String sucvipundrchar(String productId) => 'suc_vip_${productId}_undrchar';
  static const String devsucvipundrphoto = 'dev_sucvip_undrphoto';
  static String sucvipundrphoto(String productId) => 'suc_vip_${productId}_undrphoto';
  static const String devsucvipvideochat = 'dev_sucvip_videochat';
  static String sucvipvideochat(String productId) => 'suc_vip_${productId}_videochat';
  static const String devsucvipcreimg = 'dev_sucvip_creimg';
  static String sucvipcreimg(String productId) => 'suc_vip_${productId}_creimg';
  static const String devsucvipcrevideo = 'dev_sucvip_crevideo';
  static String sucvipcrevideo(String productId) => 'suc_vip_${productId}_crevideo';
  static const String devsucvipcremevip = 'dev_sucvip_mevip';
  static String sucvipcremevip(String productId) => 'suc_vip_${productId}_mevip';

  static final Analytics _instance = Analytics._internal();
  factory Analytics() => _instance;
  late FirebaseAnalytics analytics;
  //记录展示购买页时的sourceEvent和sourceScreen
  String? hotSourceEvent;
  String? hotSourceScreen;
  Analytics._internal() {
    analytics = FirebaseAnalytics.instance;
  }
  Future<bool?> logEvent(
    String eventName, {
    double? itemPrice,
    String? currency,
    String? sourceEvent,
    String? sourceChar,
    String? sourceScreen,
    String? screen,
    String? visualTag,
    int? charID,
    String? failSource,
    String? failCode,
    String? failMessage,
    String? failDetail,
  }) async {
    Map<String, Object> params = {
      'Timestamp': CommonUtil.currentTimeMillis(),
      'uid': UserService.to.isLogin ? UserService.to.userinfo.value.shareCode ?? 'NA' : 'NA',
    };
    if (itemPrice != null) {
      params['item_price'] = itemPrice;
    }
    if (currency != null) {
      params['currency'] = currency;
    }
    if (sourceEvent != null) {
      params['source_event'] = sourceEvent;
    }
    if (sourceChar != null) {
      params['source_char'] = sourceChar;
    }
    if (sourceScreen != null) {
      params['source_screen'] = sourceScreen;
    }
    if (screen != null) {
      params['screen'] = screen;
    }
    if (visualTag != null) {
      params['visual_tag'] = visualTag;
    }
    if (charID != null) {
      params['char_id'] = charID;
    }
    if (failSource != null) {
      params['fail_source'] = failSource;
    }
    if (failCode != null) {
      params['fail_code'] = failCode;
    }
    if (failMessage != null) {
      params['fail_message'] = failMessage;
    }
    if (failDetail != null) {
      params['fail_detail'] = failDetail;
    }
    if (eventName == Analytics.view && screen == Analytics.pageHot) {
      hotSourceEvent = sourceEvent;
      hotSourceScreen = sourceScreen;
    }
    // print(params.values);
    // print('analytics params:$eventName');
    // print('analytics params:$params');
    try {
      analytics.logEvent(name: eventName, parameters: params);
    } on Exception catch (e) {
      debugPrint('analytics_Error:${e.toString()}');
    }
    return false;
  }

  paySuccessEvent({String? source, required int type, required String productId}) async {
    if (source == tvipundress) {
      logEvent(type == 0 ? sucvipundress(productId) : sucgemsundress(productId));
    }
    if (source == tpaygemslocktext) {
      logEvent(type == 0 ? sucviplocktext(productId) : sucgemslocktext(productId));
    }
    if (source == tpaygemslockpic) {
      logEvent(type == 0 ? sucviplockpic(productId) : sucgemslockpic(productId));
    }
    if (source == tpaygemslockvideo) {
      logEvent(type == 0 ? sucviplockvideo(productId) : sucgemslockvideo(productId));
    }
    if (source == tpaygemslockaudio) {
      logEvent(type == 0 ? sucviplockaudio(productId) : sucgemsaudio(productId));
    }
    if (source == tpayvipcall) {
      logEvent(sucviplockcall(productId));
    }
    if (source == tpayvipacceptcall) {
      logEvent(sucviplockacceptcall(productId));
    }
    if (source == devsucvippostpic) {
      logEvent(sucvippostpic(productId));
    }
    if (source == devsucvippostvideo) {
      logEvent(sucvippostvideo(productId));
    }
    if (source == devsucvipundrchar) {
      logEvent(sucvipundrchar(productId));
    }
    if (source == devsucvipundrphoto) {
      logEvent(sucvipundrphoto(productId));
    }
    if (source == devsucvipvideochat) {
      logEvent(sucvipvideochat(productId));
    }
    if (source == devsucvipcreimg) {
      logEvent(sucvipcreimg(productId));
    }
    if (source == devsucvipcrevideo) {
      logEvent(sucvipcrevideo(productId));
    }
    if (source == devsucvipcremevip) {
      logEvent(sucvipcremevip(productId));
    }
  }
}
