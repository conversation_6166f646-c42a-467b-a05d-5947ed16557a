import 'dart:io';

import 'package:amor_app/common/apis/apis_path/apis_path.dart';
import 'package:amor_app/common/models/ai_models/ai_histroy_model.dart';
import 'package:amor_app/common/models/ai_models/ai_styles_model.dart';
import 'package:amor_app/common/models/ai_models/ai_upload_model.dart';
import 'package:amor_app/common/models/base_response/base_response_model.dart';
import 'package:amor_app/common/utils/http_util/api_request.dart';
import 'package:amor_app/common/utils/http_util/http_util.dart';
import 'package:amor_app/common/widgets/widgets.dart';
import 'package:amor_app/pages/amors/widgets/ai_photo/ai_util.dart';
import 'package:dio/dio.dart' as dio;

class AiApis {
  /// 获取风格配置
  static Future<List<AiStylesModel>> fetchStyleConf() async {
    final resp = await ApiRequest.post(api: Api.styleConf, loding: false);
    return resp == null || resp.data == null
        ? []
        : (resp.data as List).map((e) {
            return AiStylesModel.fromJson(e);
          }).toList();
  }

  /// ai生成图片历史
  static Future<List<AiHistroyModel>?> getHistory(int characterId) async {
    try {
      final baseRes = await ApiRequest.post(api: Api.aiGetHistroy, params: {"characterId": characterId}, loding: false);
      final resp = baseRes?.data["records"];
      return resp == null
          ? []
          : (resp as List).map((e) {
              return AiHistroyModel.fromJson(e);
            }).toList();
    } catch (e) {
      Loading.dismiss();
      return null;
    }
  }

  /// 上传图片, ai 图片
  /// 角色
  static Future<AiUploadRes?> uploadRoleImage({
    required String style,
    required int characterId,
  }) async {
    try {
      // 上传图片
      final formData = dio.FormData.fromMap({
        'style': style,
        'characterId': characterId,
      });
      final ops = dio.Options(
        receiveTimeout: const Duration(seconds: 160),
        contentType: 'multipart/form-data',
      );

      final response = await HttpUtils().dio.post(Api.upImageForAiImage, data: formData, options: ops);
      var baseResponse = BaseResponse.fromJson(response.data);
      final json = baseResponse.data['data'];
      final data = AiUploadRes.fromJson(json);
      return data;
    } catch (e) {
      return null;
    }
  }

  static Future<AiUploadRes?> uploadImage({
    required String imagePath,
    required String style,
  }) async {
    try {
      // 选择图片
      final file = File(imagePath);

      /// 文件 md5
      var md5 = await calculateMd5(file);

      // 压缩和转换后的文件
      final processedFile = await processImage(file);
      if (processedFile == null) {
        return null;
      }

      // 上传图片
      final formData = dio.FormData.fromMap({
        'file': await dio.MultipartFile.fromFile(
          processedFile.path,
          filename: 'img_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ),
        'style': style,
        'fileMd5': md5,
      });

      final ops = dio.Options(
        receiveTimeout: const Duration(seconds: 180),
        contentType: 'multipart/form-data',
        method: 'POST',
      );
      var response = await HttpUtils().dio.post(Api.upImageForAiImage, data: formData, options: ops);
      var baseResponse = BaseResponse.fromJson(response.data);
      final json = baseResponse.data['data'];
      // ({"code":0,"msg":"success","data":{"code":1,"message":"success","data":{"uid":"0b48c2d10ab7256a2d0d62fc5ae5ff7a","estimated_time":35}},"page":{"total":0,"pageNo":0,"pageSize":0,"pages":0},"success":true})
      final data = AiUploadRes.fromJson(json);
      return data;
    } catch (e) {
      return null;
    }
  }

  /// 获取任务结果 ai 图片
  static Future<AiResultRes?> getImageResult(String taskId, {int attempt = 0, int maxAttempts = 30}) async {
    try {
      final res = await HttpUtils().dio.post(Api.aiImageResult, queryParameters: {'taskId': taskId});
      // {"code":0,"msg":"success","data":{"code":1,"message":"success","data":{"uid":"02f8926fa7c2a3c5e5e2681362c6075f","status":2,"results":["/1950443451047292930.jpg"]}},"page":{"total":0,"pageNo":0,"pageSize":0,"pages":0},"success":true}
      var baseResponse = await HttpUtils().parseResponse(res);
      final json = baseResponse?.data['data'];

      if (json == null) {
        await Future.delayed(const Duration(seconds: 15));
        return await getImageResult(taskId, attempt: attempt + 1, maxAttempts: maxAttempts);
      } else {
        final data = AiResultRes.fromJson(json);
        if (data.status == 2) {
          return data;
        } else if (attempt < maxAttempts) {
          await Future.delayed(const Duration(seconds: 15));
          return await getImageResult(taskId, attempt: attempt + 1, maxAttempts: maxAttempts);
        } else {
          return null; // 达到最大递归次数后返回null
        }
      }
    } catch (e) {
      return null;
    }
  }

  /// 上传图片, ai 视频
  static Future<AiUploadRes?> uploadImgToVideo({
    required String imagePath,
    required String enText,
  }) async {
    try {
      // 选择图片
      final file = File(imagePath);

      /// 文件 md5
      var md5 = await calculateMd5(file);

      // 压缩和转换后的文件
      final processedFile = await processImage(file);
      if (processedFile == null) {
        return null;
      }

      // 上传图片
      final formData = dio.FormData.fromMap({
        'file': await dio.MultipartFile.fromFile(
          processedFile.path,
          filename: 'img_${DateTime.now().millisecondsSinceEpoch}.jpg',
        ),
        'style': enText,
        'fileMd5': md5,
      });

      final ops = dio.Options(
        receiveTimeout: const Duration(seconds: 180),
        contentType: 'multipart/form-data',
        method: 'POST',
      );

      final response = await HttpUtils().dio.post(Api.upImageForAiVideo, data: formData, options: ops);
      var baseResponse = BaseResponse.fromJson(response.data);
      final json = baseResponse.data['data'];
      final data = AiUploadRes.fromJson(json);
      return data;
    } catch (e) {
      return null;
    }
  }

  /// 获取任务结果 ai 视频
  static Future<AiVideoResItem?> getVideoResult(String taskId, {int attempt = 0, int maxAttempts = 30}) async {
    try {
      final res = await HttpUtils().dio.post(Api.aiVideoResult, queryParameters: {'taskId': taskId});
      /*
 {"code":0,"msg":"success","data":{"code":200,"message":"success","data":{"item":{"uid":null,"status":0,"resultPath":"https://static.amorai.net/1950799591954264066.mp4","imagePath":"https://s3.amazonaws.com/cdn.undresswith.ai/images/1dfe67dcab0361c788e4b867f93260e9.jpg","time":null,"timeNeed":0,"estimatedTime":20}}},"page":{"total":0,"pageNo":0,"pageSize":0,"pages":0},"success":true}      */
      var baseResponse = await HttpUtils().parseResponse(res);
      final json = baseResponse?.data['data'];

      if (json == null) {
        await Future.delayed(const Duration(seconds: 15));
        return await getVideoResult(taskId, attempt: attempt + 1, maxAttempts: maxAttempts);
      } else {
        final data = AiVideoResult.fromJson(json);
        final item = data.item;

        if (item != null && item.resultPath?.isNotEmpty == true) {
          return item;
        } else if (attempt < maxAttempts) {
          await Future.delayed(const Duration(seconds: 15));
          return await getVideoResult(taskId, attempt: attempt + 1, maxAttempts: maxAttempts);
        } else {
          return null; // 达到最大递归次数后返回null
        }
      }
    } catch (e) {
      return null;
    }
  }
}
