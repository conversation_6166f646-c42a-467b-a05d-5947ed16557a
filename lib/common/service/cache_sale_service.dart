import 'package:amor_app/common/apis/apis.dart';
import 'package:amor_app/common/models/profile_model/purchase_config_model.dart';
import 'package:amor_app/common/utils/utils.dart';
import 'package:amor_app/common/values/values.dart';
import 'package:amor_app/pages/profile/sub_pages/purchase/controller.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';

class CacheSaleService {
  //购买会员 服务器配置
  static PurchaseConfigModel vipConfigmodel = PurchaseConfigModel();
  static List<PurchaseItemModel> get vipConfigList => vipConfigmodel.vip?.recharges ?? [];
  static List<PurchaseItemModel> get ssvipConfigList => vipConfigmodel.ssvip?.recharges ?? [];

  //购买宝石 服务器配置
  static PurchaseConfigModel gemsConfigmodel = PurchaseConfigModel();
  static List<PurchaseItemModel> get gemsConfigList => gemsConfigmodel.gem?.recharges ?? [];
  //购买会员 商店信息
  // static List<AppPurchaseModel> storeVipConfigList = [];
  static List<AppPurchaseModel> storeSsvipConfigList = [];
  //购买宝石 商店信息
  static List<AppPurchaseModel> storeGemsConfigList = [];
  static AppPurchaseModel? selectedGemsModel;
  //货币单位
  static String priceUnit = '\$';
  //是否有优惠
  static bool hasOffer = false;
  //lite 选中的是年、月、周
  static int liteBilledIndex = 0;
  //premiun  选中的是年、月、周
  static int premiumBilledIndex = 0;
  //是否正在请求
  static bool startRequest = false;
  //缓存的昵称、头像信息
  static List avatarNickname = [];
  static cacheRequest() async {
    bool isAvailable = await InAppPurchaseUtil().initStoreInfo();
    if (isAvailable) {
      // print('订阅缓存开始');
      // print(CommonUtil.timeMillisTransition(
      //     timestamp: CommonUtil.currentTimeMillis(), format: 'yyyy-MM-dd hh:mm:ss'));
      startRequest = true;
      getPurchaseVipConfig();
      getPurchaseGemsConfig();
    }
  }

  //获取订阅配置
  static getPurchaseVipConfig() async {
    PurchaseConfigModel? result = await ProfileApis.getPurchaseVipConfig(cacheCallBack: (cache) {});
    getPlayHotConfig(result);
  }

  //获取商店Hot价格信息
  static getPlayHotConfig(PurchaseConfigModel? model) async {
    if (model == null) {
      return;
    }
    // print('获取服务器订阅信息完成');
    // print(CommonUtil.timeMillisTransition(
    //     timestamp: CommonUtil.currentTimeMillis(),
    //     format: 'yyyy-MM-dd hh:mm:ss'));
    vipConfigmodel = model;
    //请求VIP价格信息
    // List<AppPurchaseModel> vipResults =
    //     await InAppPurchaseUtil().queryProduct(serviceModelList: vipConfigList, loading: false);
    // if (vipResults.isEmpty) {
    //   return;
    // }
    /*
    print('${vipResults.length}个商品');
    vipResults.forEach((element) {
      print('订阅详情：');
      print('currencyCode:${element.productDetails!.currencyCode}');
      print('currencySymbol:${element.productDetails!.currencySymbol}');
      print('id:${element.productDetails!.id}');
      print('price:${element.productDetails!.price}');
      print('rawPrice:${element.productDetails!.rawPrice}');
      print('title:${element.productDetails!.title}');
      print('优惠订阅详情：');
      print('offerRawPrice:${element.offerProductDetails?.rawPrice}');
      print('offerPrice:${element.offerPrice}');
    });
    */
    // storeVipConfigList = vipResults;
    //设置默认选中的商品
    // for (var i = 0; i < storeVipConfigList.length; i++) {
    //   if (storeVipConfigList[i].selected == true) {
    //     liteBilledIndex = i;
    //   }
    //   //只要有一个商品包含优惠就需要弹窗
    //   if (storeVipConfigList[i].offerPrice != null) {
    //     hasOffer = true;
    //   }
    // }
    //请求SVIP价格信息
    List<AppPurchaseModel> ssvipResults =
        await InAppPurchaseUtil().queryProduct(serviceModelList: ssvipConfigList, loading: false);
    if (ssvipResults.isEmpty) {
      startRequest = false;
      return;
    }
    storeSsvipConfigList.assignAll(ssvipResults);
    /*
    AppPurchaseModel test = AppPurchaseModel(
      productDetails: ProductDetails(
        id: 'test',
        title: 'test',
        description: 'test',
        price: '19.99',
        currencyCode: 'test',
        currencySymbol: 'test',
        rawPrice: 19.99,
      ),
      serviceProductDetails: PurchaseItemModel(
        num: 30,
        subscriptionCycle: 'Monthly',
      ),
    );
    AppPurchaseModel test1 = AppPurchaseModel(
      productDetails: ProductDetails(
        id: 'test',
        title: 'test',
        description: 'test',
        price: '19.99',
        currencyCode: 'test',
        currencySymbol: 'test',
        rawPrice: 119.99,
      ),
      serviceProductDetails: PurchaseItemModel(
        num: 365,
        subscriptionCycle: 'Yearly',
      ),
    );
    AppPurchaseModel test2 = AppPurchaseModel(
      productDetails: ProductDetails(
        id: 'test',
        title: 'test',
        description: 'test',
        price: '29.99',
        currencyCode: 'test',
        currencySymbol: 'test',
        rawPrice: 299.77,
      ),
      serviceProductDetails: PurchaseItemModel(
        num: 99999,
        subscriptionCycle: 'Lifetime',
        selected: true,
        hot: true,
      ),
    );
    storeSsvipConfigList.add(test);
    storeSsvipConfigList.add(test1);
    storeSsvipConfigList.add(test2);
    */
    //设置默认选中的商品
    for (var i = 0; i < storeSsvipConfigList.length; i++) {
      if (storeSsvipConfigList[i].serviceProductDetails?.selected == true) {
        premiumBilledIndex = i;
      }
      //只要有一个商品包含优惠就需要弹窗
      if (storeSsvipConfigList[i].offerPrice != null) {
        hasOffer = true;
      }
    }
    //货币单位
    AppPurchaseModel storeProducModel = storeSsvipConfigList.elementAt(0);
    if (storeProducModel.productDetails != null) {
      priceUnit = CurrencySymbol.currencySymbolMap[storeProducModel.productDetails!.currencyCode] ?? '\$';
    }
    startRequest = false;
    if (Get.isRegistered<PurchasePageController>()) {
      PurchasePageController.to.checkCache();
    }
    // print('订阅缓存完成');
    // print(CommonUtil.timeMillisTransition(
    //     timestamp: CommonUtil.currentTimeMillis(), format: 'yyyy-MM-dd hh:mm:ss'));
  }

  //获取内购配置
  static getPurchaseGemsConfig() async {
    PurchaseConfigModel? result = await ProfileApis.getPurchaseGemsConfig(cacheCallBack: (cache) {});
    getPlayGemsConfig(result);
  }

  //获取商店Gems价格信息
  static getPlayGemsConfig(PurchaseConfigModel? model) async {
    if (model != null) {
      gemsConfigmodel = model;
      List<AppPurchaseModel> results =
          await InAppPurchaseUtil().queryProduct(serviceModelList: gemsConfigList, loading: false);
      storeGemsConfigList = results;
      if (Get.isRegistered<PurchasePageController>()) {
        PurchasePageController.to.checkCache();
      }
    }
  }

  //缓存
  static cacheNicknameAvatar() async {
    if (Get.find<SPService>().get(spCacheNicknameAvatar) != null) {
      Object? data = Get.find<SPService>().get(spCacheNicknameAvatar);
      avatarNickname = (data as Map)['data'];
    }
    if (avatarNickname.isEmpty) {
      List? result = await CommonApis.cacheNicknameAvatar();
      if (result != null) {
        avatarNickname = result;
        Get.find<SPService>().set(spCacheNicknameAvatar, {'data': result});
      }
    }
    if (avatarNickname.isNotEmpty) {
      for (var element in avatarNickname) {
        if (element['avatar'] != null) {
          try {
            DefaultCacheManager().getSingleFile(element['avatar']).then((value) {});
          } catch (e) {}
        }
      }
    }
    //缓存undress示例图片
    DefaultCacheManager().getSingleFile(undressAfter);
    DefaultCacheManager().getSingleFile(undressBefore);
    //缓存默认背景
    DefaultCacheManager().getSingleFile(chatDefaultBg);
  }
}
