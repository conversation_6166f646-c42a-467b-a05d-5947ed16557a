///全局分页大小
const int appPageSize = 50;

//AES iv
const String aAESIV = '2145214654789521';

//发送短信加密 key
const String msgAESKEY = '1zgibpmawt2adl2s';

//默认OSS服务器
const String defaultOss = 'https://static.amorai.net';
const String defaultOssBucket = 'cupid-ai';
const String defaultOssEndpoint = 'oss-us-east-1.aliyuncs.com';
//默认获取文字转语音流的服务器地址
const String defaultTtsService = 'https://speech.amorai.net';
//iOS 应用市场ID
const String iosAppStoreId = '6475957283';
//包名
const String packageName = "com.cocoai.aigf";

//用户协议
const String defaultUserAgreement = 'https://www.amorai.net/user_agreement.html';

//隐私政策
const String defaultPrivacyPolicy = 'https://www.amorai.net/privacy_policy.html';

//字体
const String fontBeVietnamPro = 'BeVietnamPro';

//adjust
const String adjustAppToken = '8hkc1j0ocp34';

//oss上传服务器路径
const String ossServicePathChat = 'chat';
const String ossServicePathClone = 'clone';
const String ossServicePathAvatar = 'avatar';

//Firebase Remote Config Key
const String undressShowCount = 'undress_show_count_new';
const String callGems = 'call_gems';
const String createImgGems = 'create_image_gems';
const String createVideoGems = 'create_video_gems';
//undress
const String undressAfter =
    'https://static.amorai.net/avatar/2024-11-14/kmvbLptsXw1731553649373.png';
const String undressBefore =
    'https://cupid-ai.oss-us-east-1.aliyuncs.com/1726232018973_1eee0a28e6bec422d0fbb6f4b8b7b2dd.jpg';
//默认背景
const String chatDefaultBg = 'https://static.amorai.net/images/sys/default_background.png';
